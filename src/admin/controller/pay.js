/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-05-11 00:56:24
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-09 03:29:15
 * @FilePath: /petshop-server/src/admin/controller/pay.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const AlipaySdk = require('alipay-sdk');
const fs = require('fs');
const path = require('path');
const Wechatpay = require('wechatpay-node-v3');
const isProd = think.env === 'production';

const alipaySdk = new AlipaySdk({
    appId: '2021005145694479',
    keyType: 'PKCS8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/private-key.pem'), 'ascii'),
    alipayPublicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/alipay-public-key.pem'), 'ascii'),
});
const wxpay_refund_notify_url = isProd ? 'http://*************:8360/api/pay/wxPayRefundNotify' : 'http://***************:8360/api/pay/wxPayRefundNotify'

const wechatpay = new Wechatpay({
    mchid: '1715440051',
    serial: '7E20A36909FD55C3776D818355C4F1CB07C27EC8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/weixin-private-key.pem'), 'ascii'),
    publicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/wexin-apiclient_cert.pem'), 'ascii'),
});

module.exports = class extends Base {
    /**
     * 同意退款，等待退货
     */
    async agentRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }

        // 待发货订单申请退款，同意后要直接退掉并取消订单
        const isPreDelivery = orderInfo.order_status == 201 || orderInfo.order_status == 300;

        if (isPreDelivery) {
            // 待发货订单直接退款并取消
            return this.refunndFunc(orderInfo, false, true);
        } else {
            // 已发货订单，同意退款，等待退货
            await this.model('order').where({
                order_sn: orderSn,
            }).update({
                order_status: 205,
            })

            // 查询当前订单的用户
            const userInfo = await this.model('user').where({
                id: orderInfo.user_id
            }).find();
            // 发货成功发送短信通知
            const orderService = think.service('order', 'admin');
            const smsError = await orderService.sendSMS(
                "2473731",
                [orderInfo.order_sn],
                [`+86${userInfo.mobile || orderInfo.mobile}`],
            );
            if (smsError) {
                think.logger.error("补充退货物流信息发送短信通知失败：", smsError);
            }

            return this.success();
        }
    }

    /**
     * 部分退款
     */
    async partialRefundAction() {
        const orderSn = this.post('orderSn');
        const refundAmount = this.post('refundAmount');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();

        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }
        // 检查退款金额是否合理
        const currentRefundedPrice = parseFloat(orderInfo.refunded_price || 0);
        const refundAmountFloat = parseFloat(refundAmount);
        const actualPrice = parseFloat(orderInfo.actual_price);

        if (refundAmountFloat <= 0) {
            return this.fail('退款金额必须大于0');
        }

        if (currentRefundedPrice + refundAmountFloat > actualPrice) {
            return this.fail(`退款金额超出限制，订单实际支付总额：${actualPrice}元；已退款：${currentRefundedPrice}元`);
        }

        let result = false;
        
        // 组合支付退款逻辑
        if (orderInfo.pay_type === 'ali+bal' || orderInfo.pay_type === 'wx+bal') {
            result = await this.processCombinationRefund(orderInfo, refundAmount);
        } else if (orderInfo.pay_type === 'ali') {
            // 支付宝退款
            const refundRequestNo = 'ali-part-refund-' + new Date().getTime() + '-' + orderInfo.order_sn;
            result = await alipaySdk.exec('alipay.trade.refund', {
                bizContent: {
                    out_trade_no: orderInfo.pay_trade_no,
                    refund_amount: refundAmount,
                    refund_reason: "订单部分退款",
                    out_request_no: refundRequestNo
                }
            });
            think.logger.info('支付宝申请部分退款', JSON.stringify(result));
            if (think.isEmpty(result)) {
                return this.fail('支付宝退款失败');
            }

            // 添加交易记录 - 发起部分退款
            await this.model('transaction_log').add({
                order_id: orderInfo.id,
                order_sn: orderInfo.order_sn,
                pay_trade_no: orderInfo.pay_trade_no,
                user_id: orderInfo.user_id,
                log_type: 'RF', // RF: 退款
                pay_status: 207, // 退款处理中
                pay_type: 'ali',
                amount: parseFloat(refundAmount),
                request_data: JSON.stringify({
                    out_request_no: refundRequestNo,
                    out_trade_no: orderInfo.pay_trade_no,
                    refund_amount: refundAmount,
                    refund_reason: "订单部分退款"
                }),
                response_data: JSON.stringify({
                    success: true,
                    message: '支付宝部分退款申请成功',
                    alipay_result: result
                })
            });
        } else if (orderInfo.pay_type === 'wx') {
            // 微信退款
            const refundNo = 'wx-part-refund-' + new Date().getTime() + '-' + orderInfo.order_sn;
            result = await wechatpay.refunds({
                out_refund_no: refundNo,
                out_trade_no: orderInfo.pay_trade_no,
                notify_url: wxpay_refund_notify_url,
                amount: {
                    refund: parseFloat(refundAmount) * 100,
                    total: parseFloat(orderInfo.actual_price) * 100,
                    currency: 'CNY'
                },
                reason: '订单部分退款'
            });
            think.logger.info('微信申请部分退款', JSON.stringify(result));
            if (think.isEmpty(result) || result.status != 200) {
                return this.fail('微信退款失败：' + (result.error || '未知错误'));
            }

            // 添加交易记录 - 发起部分退款
            await this.model('transaction_log').add({
                order_id: orderInfo.id,
                order_sn: orderInfo.order_sn,
                pay_trade_no: orderInfo.pay_trade_no,
                user_id: orderInfo.user_id,
                log_type: 'RF', // RF: 退款
                pay_status: 207, // 退款处理中
                pay_type: 'wx',
                amount: parseFloat(refundAmount),
                request_data: JSON.stringify({
                    out_refund_no: refundNo,
                    out_trade_no: orderInfo.pay_trade_no,
                    refund_amount: refundAmount,
                    reason: '订单部分退款'
                }),
                response_data: JSON.stringify({
                    success: true,
                    message: '微信部分退款申请成功',
                    wechat_result: result
                })
            });
        } else if (orderInfo.pay_type === 'balance') {
            // 余额支付的退款处理
            try {
                await this.transaction('order', async (session) => {
                    const userId = orderInfo.user_id;
                    const currentRefundedPrice = parseFloat(orderInfo.refunded_price || 0);
                    const actualPrice = parseFloat(orderInfo.actual_price);

                    // 1. 查询用户当前余额
                    const user = await this.model('user').db(session).where({
                        id: userId
                    }).find();

                    const currentBalance = parseFloat(user.balance || 0);
                    const refundAmountFloat = parseFloat(refundAmount);
                    const newBalance = currentBalance + refundAmountFloat;

                    // 2. 更新用户余额
                    await this.model('user').db(session).where({
                        id: userId
                    }).update({
                        balance: newBalance
                    });

                    // 3. 添加余额变动记录，包含备注信息
                    const remarkText = `管理员操作：订单部分退款，订单号：${orderInfo.order_sn}，退款金额：${refundAmountFloat}元`;

                    await this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: refundAmountFloat,
                        balance: newBalance,
                        log_type: 'RF', // RF: 退款
                        order_sn: orderInfo.order_sn,
                        order_id: orderInfo.id,
                        create_time: think.datetime(new Date()),
                        remark: remarkText
                    });

                    // 添加交易记录
                    await this.model('transaction_log').db(session).add({
                        order_id: orderInfo.id,
                        order_sn: orderInfo.order_sn,
                        pay_trade_no: orderInfo.pay_trade_no,
                        user_id: userId,
                        log_type: 'RF', // RF: 退款
                        pay_status: 207,
                        pay_type: 'balance',
                        amount: refundAmountFloat,
                        request_data: JSON.stringify({
                            order_sn: orderInfo.order_sn,
                            order_price: refundAmountFloat,
                            pay_trade_no: orderInfo.pay_trade_no
                        }),
                        response_data: JSON.stringify({
                            success: true,
                            message: '余额退款成功'
                        })
                    });

                    // 4. 更新订单状态和已退款金额
                    const newRefundedPrice = currentRefundedPrice + refundAmountFloat;
                    
                    // 判断是否为全额退款 - 考虑组合支付场景
                    let isFullRefund;
                    if (orderInfo.pay_type && orderInfo.pay_type.includes('+bal')) {
                        // 组合支付：判断是否退完了第三方支付部分
                        const primaryPayAmount = parseFloat(orderInfo.primary_pay_amount || 0);
                        isFullRefund = newRefundedPrice >= primaryPayAmount;
                    } else {
                        // 非组合支付：按原逻辑判断
                        isFullRefund = newRefundedPrice >= actualPrice;
                    }

                    let updateInfo = {
                        order_status: isFullRefund ? 203 : 206, // 全额退款或部分退款
                        refund_time: parseInt(new Date().getTime() / 1000),
                        refunded_price: newRefundedPrice
                    };

                    await this.model('order').db(session).where({
                        order_sn: orderInfo.order_sn,
                        refund_reason: '管理员操作：订单部分退款'
                    }).update(updateInfo);

                    think.logger.info(`用户 ${userId} 订单 ${orderInfo.order_sn} 余额退款成功，金额: ${refundAmountFloat}，当前余额: ${newBalance}`);
                });
                result = true;
            } catch (error) {
                think.logger.error('处理余额退款时出错:', error);
                return this.fail('余额退款失败: ' + error.message);
            }
        }

        return this.success();
    }

    /**
     * 订单退款
     * @return {Promise} []
     */
    async cancelRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }

        // 待发货订单退款需要同时取消订单
        const isPreDelivery = orderInfo.order_status == 201 || orderInfo.order_status == 300;
        return this.refunndFunc(orderInfo, false, isPreDelivery);
    }
    /**
     * 强制退款
     */
    async forceRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        if (think.isEmpty(orderInfo)) {
            return this.fail('订单不存在');
        }

        if (!(orderInfo.order_status == 201 || orderInfo.order_status == 202 || orderInfo.order_status == 204 ||
            orderInfo.order_status == 205 ||
            orderInfo.order_status == 300 || orderInfo.order_status == 301 || orderInfo.order_status == 302 ||
            orderInfo.order_status == 303 || orderInfo.order_status == 401)) {
            return this.fail('订单不能退款');
        }

        // 待发货订单强制退款需要同时取消订单
        const isPreDelivery = orderInfo.order_status == 201 || orderInfo.order_status == 300;
        return this.refunndFunc(orderInfo, true, isPreDelivery);
    }

    /**
     * 订单拒绝退款
     */
    async rejectRefundAction() {
        const orderSn = this.post('orderSn');
        // 订单信息
        let orderInfo = await this.model('order').where({
            order_sn: orderSn,
            order_type: 0,
        }).find();
        // 检测是否能够取消
        const handleOption = await this.model('order').getOrderHandleOption(orderInfo.id, orderInfo);
        if (!handleOption.cancel_refund) {
            return this.fail('订单不能退款');
        }
        // 拒绝退款
        await this.model('order').where({
            order_sn: orderSn,
        }).update({
            order_status: 204,
        })

        // 查询当前订单的用户
        const userInfo = await this.model('user').where({
            id: orderInfo.user_id
        }).find();
        // 发货成功发送短信通知
        const orderService = think.service('order', 'admin');
        const smsError = await orderService.sendSMS(
            "2474915",
            [orderInfo.order_sn],
            [`+86${userInfo.mobile || orderInfo.mobile}`],
        );
        if (smsError) {
            think.logger.error("拒绝退款发送短信通知失败：", smsError);
        }

        return this.success();
    }

    /**
     * 订单退款
     */
    async refunndFunc(orderInfo, isForce = false, shouldCancelOrder = false) {
        let result = false;
        let return_price = 0;
        // 有物流记录则只退回商品金额，如果没有则退回全部金额
        const expressInfo = await this.model('order_express').where({
            order_id: orderInfo.id,
            express_type: 1,
            is_delete: 0
        }).find();
        if (!think.isEmpty(expressInfo)) {
            return_price = orderInfo.actual_price - orderInfo.freight_price;
        } else {
            return_price = orderInfo.actual_price;
        }
        if (return_price <= 0) {
            return this.fail('退款金额有误');
        }
        // 组合支付和单一支付的退款处理
        if (orderInfo.pay_type === 'ali+bal' || orderInfo.pay_type === 'wx+bal') {
            // 组合支付全额退款
            result = await this.processFullCombinationRefund(orderInfo, return_price, isForce, shouldCancelOrder);
        } else if (orderInfo.pay_type === 'ali') {
            // 支付宝单一支付退款
            const refundRequestNo = 'ali-refund-' + new Date().getTime() + '-' + orderInfo.order_sn;
            result = await alipaySdk.exec('alipay.trade.refund', {
                bizContent: {
                    out_request_no: refundRequestNo,
                    out_trade_no: orderInfo.pay_trade_no,
                    refund_amount: return_price,
                    refund_reason: isForce ? '管理员退款' : "订单退款",
                }
            });
            think.logger.info('支付宝申请退款', result);
            if (think.isEmpty(result)) {
                return this.fail('支付宝退款失败');
            } else if (result.code !== '10000') {
                return this.fail("支付宝退款失败：" + result.subMsg);
            }

            // 添加交易记录 - 发起退款
            await this.model('transaction_log').add({
                order_id: orderInfo.id,
                order_sn: orderInfo.order_sn,
                pay_trade_no: orderInfo.pay_trade_no,
                user_id: orderInfo.user_id,
                log_type: 'RF', // RF: 退款
                pay_status: 207, // 退款处理中
                pay_type: 'ali',
                amount: return_price,
                request_data: JSON.stringify({
                    out_request_no: refundRequestNo,
                    out_trade_no: orderInfo.pay_trade_no,
                    refund_amount: return_price,
                    refund_reason: isForce ? '管理员退款' : "订单退款"
                }),
                response_data: JSON.stringify({
                    success: true,
                    message: '支付宝退款申请成功',
                    alipay_result: result
                })
            });

            // 更新订单状态
            await this.model('order').where({
                order_sn: orderInfo.order_sn
            }).update({
                refund_reason: isForce ? '管理员退款' : '订单退款',
            });
        } else if (orderInfo.pay_type === 'wx') {
            // 微信单一支付退款
            const refundNo = 'wx-refund-' + new Date().getTime() + '-' + orderInfo.order_sn;
            result = await wechatpay.refunds({
                out_refund_no: refundNo,
                out_trade_no: orderInfo.pay_trade_no,
                notify_url: wxpay_refund_notify_url,
                amount: {
                    refund: parseFloat(return_price) * 100,
                    total: parseFloat(orderInfo.actual_price) * 100,
                    currency: 'CNY'
                },
                reason: isForce ? '管理员退款' : '订单退款'
            });
            think.logger.info('微信申请退款', JSON.stringify(result));
            if (think.isEmpty(result)) {
                return this.fail('微信退款失败');
            } else if (result.status != 200) {
                return this.fail('微信退款失败：' + result.error);
            }

            // 添加交易记录 - 发起退款
            await this.model('transaction_log').add({
                order_id: orderInfo.id,
                order_sn: orderInfo.order_sn,
                pay_trade_no: orderInfo.pay_trade_no,
                user_id: orderInfo.user_id,
                log_type: 'RF', // RF: 退款
                pay_status: 207, // 退款处理中
                pay_type: 'wx',
                amount: return_price,
                request_data: JSON.stringify({
                    out_refund_no: refundNo,
                    out_trade_no: orderInfo.pay_trade_no,
                    refund_amount: return_price,
                    reason: isForce ? '管理员退款' : '订单退款'
                }),
                response_data: JSON.stringify({
                    success: true,
                    message: '微信退款申请成功',
                    wechat_result: result
                })
            });

            // 更新订单状态（微信退款等待回调确认）
            await this.model('order').where({
                order_sn: orderInfo.order_sn
            }).update({
                refund_reason: isForce ? '管理员退款' : '订单退款'
            });
        } else if (orderInfo.pay_type === 'balance') {
            // 余额支付的退款处理
            try {
                await this.transaction('order', async (session) => {
                    const userId = orderInfo.user_id;

                    // 1. 查询用户当前余额
                    const user = await this.model('user').db(session).where({
                        id: userId
                    }).find();

                    const currentBalance = parseFloat(user.balance || 0);
                    const refundAmount = parseFloat(return_price);
                    const newBalance = currentBalance + refundAmount;

                    // 2. 更新用户余额
                    await this.model('user').db(session).where({
                        id: userId
                    }).update({
                        balance: newBalance
                    });

                    // 3. 添加余额变动记录，包含备注信息
                    const remarkText = `管理员操作：订单全额退款，订单号：${orderInfo.order_sn}，退款金额：${refundAmount}元`;

                    await this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: refundAmount,
                        balance: newBalance,
                        log_type: 'RF', // RF: 退款
                        order_sn: orderInfo.order_sn,
                        order_id: orderInfo.id,
                        create_time: think.datetime(new Date()),
                        remark: remarkText
                    });

                    // 添加交易记录
                    await this.model('transaction_log').db(session).add({
                        order_id: orderInfo.id,
                        order_sn: orderInfo.order_sn,
                        pay_trade_no: orderInfo.pay_trade_no, // 新增：关联原支付流水号
                        user_id: userId,
                        log_type: 'RF', // RF: 退款
                        pay_status: 203,
                        pay_type: 'balance',
                        amount: refundAmount,
                        request_data: JSON.stringify({
                            order_sn: orderInfo.order_sn,
                            order_price: refundAmount,
                            pay_trade_no: orderInfo.pay_trade_no
                        }),
                        response_data: JSON.stringify({
                            success: true,
                            message: '余额退款成功'
                        })
                    })

                    // 4. 更新订单状态和已退款金额
                    const currentRefundedPrice = parseFloat(orderInfo.refunded_price || 0);
                    const newRefundedPrice = currentRefundedPrice + refundAmount;

                    await this.model('order').db(session).where({
                        order_sn: orderInfo.order_sn
                    }).update({
                        order_status: 203,
                        refund_reason: isForce ? '管理员退款' : '订单退款',
                        refund_time: parseInt(new Date().getTime() / 1000),
                        refunded_price: newRefundedPrice
                    });

                    think.logger.info(`用户 ${userId} 订单 ${orderInfo.order_sn} 余额退款成功，金额: ${refundAmount}，当前余额: ${newBalance}`);
                });
                result = true;
            } catch (error) {
                think.logger.error('处理余额退款时出错:', error);
                return this.fail('余额退款失败: ' + error.message);
            }
        }

        return this.success();
    }

    /**
     * 处理组合支付部分退款
     */
    async processCombinationRefund(orderInfo, refundAmount) {
        const refundAmountFloat = parseFloat(refundAmount);
        const balancePayAmount = parseFloat(orderInfo.balance_pay_amount || 0);
        const primaryPayAmount = parseFloat(orderInfo.primary_pay_amount || 0);
        const currentRefundedPrice = parseFloat(orderInfo.refunded_price || 0);
        const actualPrice = parseFloat(orderInfo.actual_price);

        try {
            await this.transaction('order', async (session) => {
                const userId = orderInfo.user_id;
                let balanceRefundAmount = 0;
                let externalRefundAmount = 0;

                // 计算退款分配：优先退余额部分，再退外部支付部分
                if (refundAmountFloat <= balancePayAmount) {
                    // 只退余额部分
                    balanceRefundAmount = refundAmountFloat;
                    externalRefundAmount = 0;
                } else {
                    // 余额全退，剩余部分从外部支付退
                    balanceRefundAmount = balancePayAmount;
                    externalRefundAmount = refundAmountFloat - balancePayAmount;
                }

                // 1. 处理余额退款
                if (balanceRefundAmount > 0) {
                    const user = await this.model('user').db(session).where({ id: userId }).find();
                    const currentBalance = parseFloat(user.balance || 0);
                    const newBalance = currentBalance + balanceRefundAmount;

                    await this.model('user').db(session).where({ id: userId }).update({
                        balance: newBalance
                    });

                    await this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: balanceRefundAmount,
                        balance: newBalance,
                        log_type: 'RF',
                        order_sn: orderInfo.order_sn,
                        order_id: orderInfo.id,
                        remark: `组合支付退款-余额部分：${orderInfo.order_sn}，退款金额：${balanceRefundAmount}元`
                    });
                }

                // 2. 处理外部支付退款
                if (externalRefundAmount > 0) {
                    if (orderInfo.pay_type === 'ali+bal') {
                        const externalResult = await alipaySdk.exec('alipay.trade.refund', {
                            bizContent: {
                                out_trade_no: orderInfo.pay_trade_no,
                                refund_amount: externalRefundAmount.toFixed(2),
                                refund_reason: "组合支付订单退款",
                                out_request_no: 'ali-combo-refund-' + new Date().getTime() + '-' + orderInfo.order_sn
                            }
                        });
                        
                        if (think.isEmpty(externalResult) || externalResult.code !== '10000') {
                            throw new Error('支付宝退款失败：' + (externalResult.subMsg || '未知错误'));
                        }
                    } else if (orderInfo.pay_type === 'wx+bal') {
                        const externalResult = await wechatpay.refunds({
                            out_refund_no: 'wx-combo-refund-' + new Date().getTime() + '-' + orderInfo.order_sn,
                            out_trade_no: orderInfo.pay_trade_no,
                            notify_url: wxpay_refund_notify_url,
                            amount: {
                                refund: Math.round(externalRefundAmount * 100),
                                total: Math.round(primaryPayAmount * 100),
                                currency: 'CNY'
                            },
                            reason: '组合支付订单退款'
                        });
                        
                        if (think.isEmpty(externalResult) || externalResult.status != 200) {
                            throw new Error('微信退款失败：' + (externalResult.error || '未知错误'));
                        }
                    }
                }

                // 3. 添加交易记录
                await this.model('transaction_log').db(session).add({
                    order_id: orderInfo.id,
                    order_sn: orderInfo.order_sn,
                    pay_trade_no: orderInfo.pay_trade_no,
                    user_id: userId,
                    log_type: 'RF',
                    pay_status: 207,
                    pay_type: orderInfo.pay_type,
                    amount: externalRefundAmount,
                    balance_pay_amount: balanceRefundAmount,
                    request_data: JSON.stringify({
                        order_sn: orderInfo.order_sn,
                        total_refund: refundAmountFloat,
                        balance_refund: balanceRefundAmount,
                        external_refund: externalRefundAmount
                    }),
                    response_data: JSON.stringify({
                        success: true,
                        message: '组合支付退款成功'
                    })
                });

                // 4. 更新订单状态
                const newRefundedPrice = currentRefundedPrice + refundAmountFloat;
                
                // 判断是否为全额退款 - 考虑组合支付场景
                let isFullRefund;
                if (orderInfo.pay_type && orderInfo.pay_type.includes('+bal')) {
                    // 组合支付：判断是否退完了第三方支付部分
                    const primaryPayAmount = parseFloat(orderInfo.primary_pay_amount || 0);
                    isFullRefund = newRefundedPrice >= primaryPayAmount;
                } else {
                    // 非组合支付：按原逻辑判断
                    isFullRefund = newRefundedPrice >= actualPrice;
                }

                await this.model('order').db(session).where({ order_sn: orderInfo.order_sn }).update({
                    order_status: isFullRefund ? 203 : 206,
                    refund_time: parseInt(new Date().getTime() / 1000),
                    refunded_price: newRefundedPrice
                });

                think.logger.info(`组合支付部分退款成功，订单：${orderInfo.order_sn}，退款金额：${refundAmountFloat}，余额部分：${balanceRefundAmount}，主支付部分：${externalRefundAmount}`);
            });

            return true;
        } catch (error) {
            think.logger.error('处理组合支付退款时出错:', error);
            throw error;
        }
    }

    /**
     * 处理组合支付的全额退款
     */
    async processFullCombinationRefund(orderInfo, refundAmount, isForce = false, shouldCancelOrder = false) {
        const balancePayAmount = parseFloat(orderInfo.balance_pay_amount || 0);
        const primaryPayAmount = parseFloat(orderInfo.primary_pay_amount || 0);
        const refundReason = isForce ? '管理员退款' : '订单退款';

        try {
            await this.transaction('order', async (session) => {
                const userId = orderInfo.user_id;

                // 1. 处理余额退款（全部退还）
                if (balancePayAmount > 0) {
                    const user = await this.model('user').db(session).where({ id: userId }).find();
                    const currentBalance = parseFloat(user.balance || 0);
                    const newBalance = currentBalance + balancePayAmount;

                    await this.model('user').db(session).where({ id: userId }).update({
                        balance: newBalance
                    });

                    await this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: balancePayAmount,
                        balance: newBalance,
                        log_type: 'RF',
                        order_sn: orderInfo.order_sn,
                        order_id: orderInfo.id,
                        remark: `${refundReason}-余额部分：${orderInfo.order_sn}，退款金额：${balancePayAmount}元`
                    });
                }

                // 2. 处理外部支付退款（全部退还）
                if (primaryPayAmount > 0) {
                    if (orderInfo.pay_type === 'ali+bal') {
                        const externalResult = await alipaySdk.exec('alipay.trade.refund', {
                            bizContent: {
                                out_trade_no: orderInfo.pay_trade_no,
                                refund_amount: primaryPayAmount.toFixed(2),
                                refund_reason: refundReason,
                                out_request_no: 'ali-full-refund-' + new Date().getTime() + '-' + orderInfo.order_sn
                            }
                        });
                        
                        if (think.isEmpty(externalResult) || externalResult.code !== '10000') {
                            throw new Error('支付宝退款失败：' + (externalResult.subMsg || '未知错误'));
                        }
                    } else if (orderInfo.pay_type === 'wx+bal') {
                        const externalResult = await wechatpay.refunds({
                            out_refund_no: 'wx-full-refund-' + new Date().getTime() + '-' + orderInfo.order_sn,
                            out_trade_no: orderInfo.pay_trade_no,
                            notify_url: wxpay_refund_notify_url,
                            amount: {
                                refund: Math.round(primaryPayAmount * 100),
                                total: Math.round(primaryPayAmount * 100),
                                currency: 'CNY'
                            },
                            reason: refundReason
                        });
                        
                        if (think.isEmpty(externalResult) || externalResult.status != 200) {
                            throw new Error('微信退款失败：' + (externalResult.error || '未知错误'));
                        }
                    }
                }

                // 3. 添加交易记录
                await this.model('transaction_log').db(session).add({
                    order_id: orderInfo.id,
                    order_sn: orderInfo.order_sn,
                    pay_trade_no: orderInfo.pay_trade_no,
                    user_id: userId,
                    log_type: 'RF',
                    pay_status: 207, // 退款处理中
                    pay_type: orderInfo.pay_type,
                    amount: primaryPayAmount,
                    balance_pay_amount: balancePayAmount,
                    request_data: JSON.stringify({
                        order_sn: orderInfo.order_sn,
                        total_refund: refundAmount,
                        balance_refund: balancePayAmount,
                        external_refund: primaryPayAmount,
                        reason: refundReason
                    }),
                    response_data: JSON.stringify({
                        success: true,
                        message: '组合支付全额退款成功'
                    })
                });

                // 4. 更新订单状态
                await this.model('order').db(session).where({ order_sn: orderInfo.order_sn }).update({
                    order_status: 203,
                    refund_time: parseInt(new Date().getTime() / 1000),
                    refund_reason: refundReason,
                    refunded_price: balancePayAmount
                });
            });

            think.logger.info(`组合支付全额退款成功，订单：${orderInfo.order_sn}，退款原因：${refundReason}，退款金额：${refundAmount}，余额部分：${balancePayAmount}，主支付部分：${primaryPayAmount}`);
            return true;
        } catch (error) {
            think.logger.error('处理组合支付全额退款时出错:', error);
            throw error;
        }
    }
};
