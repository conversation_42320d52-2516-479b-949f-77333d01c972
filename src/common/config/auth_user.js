/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-03-26 14:44:26
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-04 00:55:34
 * @FilePath: /petshop-server/src/common/config/auth_user.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/middleware/auth_user.js
const {throwError} = require("./utils");
module.exports = (options, app) => {
  return async (ctx, next) => {
    // 可匿名访问但支持 token 软验证的接口列表
    const softVerifyPaths = [
      '/api/settings/showSettings',
      '/api/index/appInfo',
      '/api/catalog/index',
      '/api/goods/count',
      '/api/catalog/currentlist',
      '/api/catalog/getGoodsBrandByCategoryId',

      '/api/cart/goodsCount',
      '/api/goods/detail',
      '/api/search/index',

      "/api/index/checkUpdate"
    ];
    const currentPath = ctx.path;

    const token = ctx.header['x-auth-token'] || '';
    if (!token) {
      if (softVerifyPaths.includes(currentPath)) {
        // 是软验证接口 → 不抛错，只当作匿名访问
        ctx.state.user = null;

        return await next();
      } else {
        // 强验证接口 → 抛出 401
        return ctx.throw(throwError(401, "token不存在"));
      }
    }

		let tokenSerivce = think.service('token', 'api');
    const userType = tokenSerivce.getUserType(token);
    if (!userType) {
      return ctx.throw(throwError(401, "token无效"));
    }
    if (userType == 'user') {
      tokenSerivce = think.service('token', 'api');
    }else{
      tokenSerivce = think.service('token', 'admin');
    }
    const uid = await tokenSerivce.getUserId(token);
    
    if (!uid) { 
      return ctx.throw(throwError(401, "token无效"));
    }

    // 生成缓存键
    const cacheKey = `user_${uid}`;
    
    try {
      // 尝试从缓存读取
      let user = await ctx.cache(cacheKey);
      if (think.isEmpty(user)) {
        if (userType == 'user') {
          user = await ctx.model('user').
              field("id,nickname, avatar, mobile, balance, is_agent, pet_birthday").
              where({ id: uid, is_delete: 0 }).find();
        }else{
          user = await ctx.model('admin').
              field("id,username").
              where({ id: uid, is_delete: 0 }).find();
        }
        
        if (think.isEmpty(user)) { 
          return ctx.throw(throwError(401, "用户不存在"));
        }
        // 缓存用户信息（有效期 1 小时）
        await think.cache(cacheKey, user, { timeout: 60000 * 60 });
      }
      ctx.state.user = user;
    } catch (error) {
      think.logger.error("缓存读取失败", error);
      return ctx.throw(throwError(401, "用户不存在"));
    }
    
    await next();
  };
};