/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-05 01:34:49
 * @FilePath: /petshop-server/src/api/controller/crontab.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const moment = require('moment');
const rp = require('request-promise');
const http = require("http");
module.exports = class extends Base {
    async timetaskAction() {
        console.log("=============开始============");
        let currentTime = parseInt(new Date().getTime() / 1000);
        let newday = new Date(new Date().setHours(3, 0, 0, 0)) / 1000;
        let newday_over = new Date(new Date().setHours(3, 0, 59, 0)) / 1000;
        if (currentTime > newday && currentTime < newday_over) {
        }
        // 将公告下掉
        let notice = await this.model('notice').where({
            is_delete: 0
        }).select();
        if (notice.length > 0) {
            for (const noticeItem of notice) {
                let notice_exptime = noticeItem.end_time;
                if (currentTime > notice_exptime) {
                    await this.model('notice').where({
                        id: noticeItem.id
                    }).update({
                        is_delete: 1
                    });
                }
            }
        }
        // 超时订单取消支付
        const expiretime = parseInt(new Date().getTime() / 1000) - 24 * 60 * 60;
        let orderList = await this.model('order').field('id, user_id, coupon_code, coupon_template_id').where({
            order_status: ['IN', '101,801'],
            add_time: ['<', expiretime],
            is_delete: 0,
        }).select();
        if (orderList.length != 0) {
            for (const item of orderList) {
                let orderId = item.id;

                try {
                    await this.transaction('order', async (session) => {
                        // 更新订单状态为取消
                        await this.model('order').db(session).where({
                            id: orderId
                        }).update({
                            order_status: 102
                        });

                        // 如果使用了优惠券，需要退还优惠券
                        if (item.coupon_code && item.coupon_template_id > 0) {
                            const couponService = this.service('coupon');
                            const refundResult = await couponService.refundCoupon(item.coupon_code, item.user_id);
                            if (!refundResult) {
                                think.logger.error(`定时任务取消订单时退还优惠券失败，订单ID: ${orderId}, 优惠券码: ${item.coupon_code}`);
                            } else {
                                think.logger.info(`定时任务取消订单成功退还优惠券，订单ID: ${orderId}, 优惠券码: ${item.coupon_code}`);
                            }
                        }
                    });
                } catch (error) {
                    think.logger.error(`定时任务取消订单失败，订单ID: ${orderId}:`, error.message);
                    think.logger.error('错误堆栈:', error.stack);
                    think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
                }
            }
        }
        // 定时将到期的广告停掉
        let ad_info = await this.model('ad').where({
            end_time: ['<', currentTime],
            enabled: 1
        }).select();
        if (ad_info.length != 0) {
            await this.model('ad').where({
                id: ['IN', ad_info.map((ele) => ele.id)]
            }).update({
                enabled: 0
            });
        }
        //定时将长时间没收货的订单确认收货
        const noConfirmTime = parseInt(new Date().getTime() / 1000) - 5 * 24 * 60 * 60;
        // 5天没确认收货就自动确认
        let noConfirmList = await this.model('order').where({
            order_status: 301,
            shipping_time: {
                '<=': noConfirmTime,
                '<>': 0
            },
            is_delete: 0,
        }).select();
        if (noConfirmList.length != 0) {
            for (const citem of noConfirmList) {
                let orderId = citem.id;
                await this.model('order').where({
                    id: orderId
                }).update({
                    order_status: 401,
                    confirm_time: currentTime
                });
            }
        }
    }
};