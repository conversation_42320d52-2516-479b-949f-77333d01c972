/*
 * @Author: <PERSON><PERSON> <EMAIL>
 * @Date: 2025-03-10 23:28:42
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-08-27 00:16:54
 * @FilePath: /petshop-server/src/api/controller/address.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const Base = require('../../common/controller/base.js');
const _ = require('lodash');
const md5 = require('md5');
const axios = require('axios');
const qs = require('qs');

module.exports = class extends Base {
    async getAddressesAction() {
		const userId = this.getLoginUserId();;
        const addressList = await this.model('address').where({
            user_id: userId,
            is_delete: 0
        }).order('id desc').select();
        let itemKey = 0;
        for (const addressItem of addressList) {
            addressList[itemKey].province_name = await this.model('region').getRegionName(addressItem.province_id);
            addressList[itemKey].city_name = await this.model('region').getRegionName(addressItem.city_id);
            addressList[itemKey].district_name = await this.model('region').getRegionName(addressItem.district_id);
            addressList[itemKey].full_region = addressList[itemKey].province_name + addressList[itemKey].city_name + addressList[itemKey].district_name;
            itemKey += 1;
        }
        return this.success(addressList);
    }
    async saveAddressAction() {
        let addressId = this.post('id');
		const userId = this.getLoginUserId();;
        const addressData = {
            name: this.post('name'),
            mobile: this.post('mobile'),
            province_id: this.post('province_id'),
            city_id: this.post('city_id'),
            district_id: this.post('district_id'),
            address: this.post('address'),
            user_id: this.getLoginUserId(),
            is_default: this.post('is_default')
        };
        if (think.isEmpty(addressId)) {
            addressId = await this.model('address').add(addressData);
        } else {
            await this.model('address').where({
                id: addressId,
                user_id: userId
            }).update(addressData);
        }
        // 如果设置为默认，则取消其它的默认
        if (this.post('is_default') == 1) {
            await this.model('address').where({
                id: ['<>', addressId],
                user_id: userId
            }).update({
                is_default: 0
            });
        }
        const addressInfo = await this.model('address').where({
            id: addressId
        }).find();
        return this.success(addressInfo);
    }
    async deleteAddressAction() {
        const id = this.post('id');
		const userId = this.getLoginUserId();;
        let d = await this.model('address').where({
            user_id: userId,
            id: id
        }).update({
            is_delete: 1
        });
        return this.success(d);
    }
    async addressDetailAction() {
        const addressId = this.get('id');
		const userId = this.getLoginUserId();;
        const addressInfo = await this.model('address').where({
            user_id: userId,
            id: addressId
        }).find();
        if (!think.isEmpty(addressInfo)) {
            addressInfo.province_name = await this.model('region').getRegionName(addressInfo.province_id);
            addressInfo.city_name = await this.model('region').getRegionName(addressInfo.city_id);
            addressInfo.district_name = await this.model('region').getRegionName(addressInfo.district_id);
            addressInfo.full_region = addressInfo.province_name + addressInfo.city_name + addressInfo.district_name;
        }
        return this.success(addressInfo);
    }

    /**
     * 快递100 解析地址
     */
    async addressResolutionAction() {
        const address = this.get('address');
        const addressResolutionUrl = think.config('kuaidi100.addressResolutionUrl');
        const printKey = think.config('kuaidi100.key');
        const printSecret = think.config('kuaidi100.secret');
        let paramObj = {
            content: address,
        };
        // 当前时间戳
        const t = new  Date().valueOf();
        // 请求签名 32位大写，签名，用于验证身份，按MD5 (param +t+key+ secret)的顺序进行MD5加密，不需要加上“+”号，
        const sign = md5(JSON.stringify(paramObj) + t + printKey + printSecret).toUpperCase();

        const postBody = {
            key: printKey,
            sign: sign,
            t: t,
            param: JSON.stringify(paramObj) // 这里同样要 stringify
        };

        think.logger.info("请求解析地址快递100入参：", postBody);
        // 发起 POST 请求（表单方式）
        const response = await axios.post(addressResolutionUrl, qs.stringify(postBody), {
            headers: {
                'Content-Type': 'application/x-www-form-urlencoded'
            }
        });
        think.logger.info("请求解析地址快递100响应：", JSON.stringify(response.data));
        if (response.data.code != 200 && !(response.data.data.result && response.data.data.result.length > 0)) {
            return this.fail("识别地址失败");
        }
        const addRes = response.data.data.result[0];
        // 详细地址
        const subArea = addRes.xzq.subArea || "";
        // province
        const province = addRes.xzq.province || "";
        // city
        const city = addRes.xzq.city || "";
        // district
        const district = addRes.xzq.district || "";
        // mobile
        let mobile = "";
        if (addRes.mobile && addRes.mobile.length > 0) {
            mobile = addRes.mobile[0];
        }
        const name = addRes.name || "";

        // 查库找到对应的code 和 name
        const regionModel = this.model('region');
        const provinceInfo = await regionModel.where({
            name: province
        }).find();
        const cityInfo = await regionModel.where({
            name: city
        }).find();
        const districtInfo = await regionModel.where({
            name: district
        }).find();

        return this.success({
            name: name,
            mobile: mobile,
            province_id: provinceInfo.id,
            city_id: cityInfo.id,
            district_id: districtInfo.id,
            address: subArea,
            province_name: provinceInfo.name,
            city_name: cityInfo.name,
            district_name: districtInfo.name,
            full_region: provinceInfo.name + cityInfo.name + districtInfo.name
        });
    }
};