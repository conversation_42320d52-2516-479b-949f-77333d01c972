const Base = require('../../common/controller/base.js');
const moment = require('moment');
const generate = require('nanoid/generate');
const AlipaySdk = require('alipay-sdk');
const fs = require('fs');
const path = require('path');
const isProd = think.env === 'production';
const Wechatpay = require('wechatpay-node-v3');

const alipaySdk = new AlipaySdk({
    appId: '2021005145694479',
    keyType: 'PKCS8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/private-key.pem'), 'ascii'),
    alipayPublicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/alipay-public-key.pem'), 'ascii'),
});
const alipay_notify_url = isProd ? 'http://*************:8360/api/pay/aliPayNotify' : 'http://***************:8360/api/pay/aliPayNotify'
const wxpay_notify_url = isProd ? 'http://*************:8360/api/pay/wxPayNotify' : 'http://***************:8360/api/pay/wxPayNotify'
const wxpay_refund_notify_url = isProd ? 'http://*************:8360/api/pay/wxPayRefundNotify' : 'http://***************:8360/api/pay/wxPayRefundNotify'

const wechatpay = new Wechatpay({
    mchid: '1715440051',
    serial: '7E20A36909FD55C3776D818355C4F1CB07C27EC8',
    privateKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/weixin-private-key.pem'), 'ascii'),
    publicKey: fs.readFileSync(path.join(think.ROOT_PATH, 'src/common/config/wexin-apiclient_cert.pem'), 'ascii'),
});

module.exports = class extends Base {

    //todo 查询订单状态的接口改成 查询支付宝和微信接口

    async prePay() {
        const orderId = this.post('orderId');
        const userAgent = this.getUserAgent();
        const orderInfo = await this.model('order').where({
            id: orderId
        }).find();
        // 再次确认库存和价格
        let orderGoods = await this.model('order_goods').where({
            order_id: orderId,
            is_delete: 0
        }).select();
        let checkPrice = 0;
        let checkStock = 0;
        for (const item of orderGoods) {
            let product = await this.model('product').where({
                id: item.product_id
            }).find();
            if (item.number > product.goods_number) {
                checkStock++;
            }
            const realPrice = userAgent ? product.agent_price : product.retail_price;
            if (item.retail_price != realPrice) {
                checkPrice++;
            }
        }
        if (checkStock > 0) {
            return '库存不足，请重新下单';
        }
        if (checkPrice > 0) {
            return '价格发生变化，请重新下单';
        }
        if (think.isEmpty(orderInfo)) {
            return '订单已取消';
        }
        if (parseInt(orderInfo.order_status) != 101) {
            return "订单状态有误";
        }
        if (orderInfo.offline_pay == 1) {
            return "该订单为线下付款，不能使用在线支付";
        }

        return "";
    }

    async weixinPayAction() {
        const order_sn = this.post('orderSn');
        const orderId = this.post('orderId');
        const apiVersion = this.post('apiVersion');

        if (!orderId || !order_sn) {
            return this.fail(-1, '参数错误');
        }
        const checkPre = await this.prePay();
        if (checkPre) {
            return this.fail(400, checkPre);
        }

        // order_price 查出该订单的实际需要支付金额
        let orderWhere = {}
        if (orderId) {
            orderWhere.id = orderId;
        }
        if (order_sn) {
            orderWhere.order_sn = order_sn;
        }
        const orderInfo = await this.model('order').where(orderWhere).find();
        if (think.isEmpty(orderInfo)) {
            return this.fail(-1, '订单不存在');
        }
        // 生产环境必须是订单数据查询得到金额，测试环境可以是传入金额
        const order_price = isProd ? (orderInfo.actual_price || 0) : (this.post("orderPrice") || 0);

        if (order_price <= 0) {
            return this.fail(-1, '订单金额有误');
        }

        // 生成支付流水号，避免金额变化时的订单号重复问题
        // 格式：原订单号-时间戳-随机数
        const randomSuffix = Math.random().toString(36).substr(2, 5);
        const pay_trade_no = `${order_sn}-WX-${randomSuffix}`;

        try {
            const result = await wechatpay.transactions_app({
                appid: 'wxf54e017f3b833a82',
                description: '唯优众宠订单',
                out_trade_no: pay_trade_no, // 使用支付流水号而不是订单号
                amount: {
                    total: parseFloat(order_price) * 100
                },
                notify_url: wxpay_notify_url
            });

            think.logger.info('微信支付下单结果：', result);

            if (result.status != 200) {
                return this.fail(400, result.error);
            }

            // 记录支付流水号到订单表，用于后续查询和回调处理
            await this.model('order').where({ id: orderId }).update({
                pay_trade_no: pay_trade_no,
                pay_type: 'wx'
            });

            // 添加交易记录 - 发起支付
            await this.model('transaction_log').add({
                order_id: orderId,
                order_sn: order_sn,
                pay_trade_no: pay_trade_no,
                user_id: orderInfo.user_id,
                log_type: 'CS', // CS: 消费
                pay_status: 101, // 未付款
                pay_type: 'wx',
                amount: order_price,
                request_data: JSON.stringify({
                    order_sn: order_sn,
                    order_price: order_price,
                    pay_trade_no: pay_trade_no,
                    appid: 'wxf54e017f3b833a82'
                }),
                response_data: JSON.stringify({
                    success: true,
                    message: '微信支付下单成功',
                    prepay_id: result.data.prepay_id || ''
                })
            });

            // 返回支付参数，同时返回支付流水号供前端使用
            if (apiVersion == '1.1.1') {
                return this.success({
                    wxPayInfo: result.data,
                    pay_trade_no: pay_trade_no,
                    order_sn: order_sn,
                    order_price: order_price
                });
            } else {
                return this.success(result.data);
            }
        } catch (error) {
            think.logger.error('微信支付下单失败:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return this.fail(400, error.message || '微信支付下单失败');
        }
    }

    async wxPayNotifyAction() {
        const resource = this.post('resource');

        think.logger.info(`原始微信回调resource，: ${JSON.stringify(resource)}`);
        const result = wechatpay.decipher_gcm(
            resource.ciphertext, resource.associated_data, resource.nonce, think.config('weixin.partner_key'));

        think.logger.info(`解密微信回调result，: ${JSON.stringify(result)}`);

        if (!result) {
            think.logger.error('微信支付回调解密失败');
            return this.json({ code: 400, message: '微信支付回调解密失败' });
        }

        if (result.trade_state === 'SUCCESS') {
            // 判断是否为充值订单
            if (result.out_trade_no.startsWith('RC')) {
                const recharge = await this.afterRechargePayment(result);
                if (!recharge) {
                    think.logger.error('微信充值回调处理失败');
                    return this.json({ code: 400, message: '微信充值回调处理失败' });
                }
            } else {
                const order = await this.afterWxPay(result);
                if (!order) {
                    think.logger.error('微信订单回调处理失败');
                    return this.json({ code: 400, message: '微信订单回调处理失败' });
                }
            }
            return this.json({ code: 200, message: 'success' });
        }
        return this.json({ code: 200, message: 'success' });
    }

    async wxPayRefundNotifyAction() {
        const resource = this.post('resource');

        think.logger.info(`原始微信退款回调resource，: ${JSON.stringify(resource)}`);
        const result = wechatpay.decipher_gcm(
            resource.ciphertext, resource.associated_data, resource.nonce, think.config('weixin.partner_key'));

        think.logger.info(`解密微信退款回调result，: ${JSON.stringify(result)}`);

        if (!result) {
            think.logger.error('微信退款回调解密失败');
            return this.json({ code: 400, message: '微信退款回调解密失败' });
        }
        if (result.refund_status === 'SUCCESS') {
            // 判断是否为充值订单
            if (result.out_refund_no.startsWith('RC')) {
                //todo 处理充值退款，如果需要的话
                think.logger.info('微信充值退款回调，暂未实现处理逻辑');
            } else {
                const refund = await this.afterWxPayRefund(result);
                if (!refund) {
                    think.logger.error('微信退款回调处理失败');
                    return this.json({ code: 400, message: '微信退款回调处理失败' });
                }
            }
            return this.json({ code: 200, message: 'success' });
        }
        return this.json({ code: 400, message: '' });
    }

    async afterWxPay(orderInfo) {
        try {
            await this.transaction("order", async session => {
                const orderModel = this.model('order').db(session);
                const orderGoodsModel = this.model('order_goods').db(session);
                const goodsModel = this.model('goods').db(session);
                const productModel = this.model('product').db(session);

                // 记录支付流水号
                const payTradeNo = orderInfo.out_trade_no;
                think.logger.info(`处理微信回调，支付流水号: ${payTradeNo}`);

                // 1. 查找订单 - 现在使用支付流水号查找
                let order = await orderModel.where({
                    pay_trade_no: payTradeNo,
                    is_delete: 0
                }).find();

                // 兼容旧的订单号查找方式（向后兼容）
                if (think.isEmpty(order)) {
                    order = await orderModel.where({
                        order_sn: payTradeNo,
                        is_delete: 0
                    }).find();
                }

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单，支付流水号: ${payTradeNo}`);
                    return true;
                }

                // 2. 幂等判断：如果已支付，不重复处理
                if (order.order_status >= 201) {
                    think.logger.info(`订单 ${order.order_sn} 已经处理过支付，当前状态: ${order.order_status}`);
                    return true;
                }

                // 3. 判断是否为组合支付
                const isCombinationPay = payTradeNo.includes('-WXBAL-');
                let payType = 'wx';
                let actualPrice = orderInfo.amount.total / 100;

                if (isCombinationPay) {
                    payType = 'wx+bal';
                    actualPrice = parseFloat(order.actual_price); // 使用订单原始金额

                    // 组合支付：扣减用户余额
                    const balanceAmount = parseFloat(order.balance_pay_amount || 0);
                    if (balanceAmount > 0) {
                        const user = await this.model('user').db(session).where({ id: order.user_id }).find();
                        const currentBalance = parseFloat(user.balance || 0);

                        if (currentBalance < balanceAmount) {
                            // 余额不足，发送短信通知并发起退款
                            await this.sendBalanceInsufficientSMS(order.user_id, order.order_sn, 'wx+bal');
                            think.logger.error(`组合支付余额不足，订单：${order.order_sn}，需要：${balanceAmount}，当前：${currentBalance}`);

                            // 发起微信退款
                            await this.processBalanceInsufficientRefund(order, orderInfo, 'wx');
                            return true; // 返回成功避免重复回调，但订单状态不会更新为已支付
                        }

                        const newBalance = currentBalance - balanceAmount;

                        // 扣减用户余额
                        await this.model('user').db(session).where({ id: order.user_id }).update({
                            balance: newBalance
                        });

                        // 添加余额变动记录
                        await this.model('balance_log').db(session).add({
                            user_id: order.user_id,
                            amount: balanceAmount,
                            balance: newBalance,
                            log_type: 'CS',
                            related_pay_type: 'wx',
                            order_id: order.id,
                            order_sn: order.order_sn,
                            remark: `组合支付-余额部分：${order.order_sn}`
                        });
                    }
                }

                // 4. 获取订单商品明细（提前获取，减少事务内查询）
                const orderGoodsList = await orderGoodsModel.where({ order_id: order.id }).select();

                // 5. 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];

                // 更新订单状态为已支付
                updatePromises.push(
                    orderModel.where({ id: order.id }).update({
                        order_status: 201, // 已付款
                        pay_type: payType,
                        pay_time: Math.floor(Date.now() / 1000),
                        pay_id: orderInfo.transaction_id,
                        actual_price: actualPrice
                    })
                );

                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        order_sn: order.order_sn,
                        pay_trade_no: payTradeNo,
                        user_id: order.user_id,
                        log_type: 'CS', // CS: 消费
                        pay_status: 201, // 已付款
                        pay_type: payType,
                        amount: isCombinationPay ? orderInfo.amount.total / 100 : orderInfo.amount.total / 100,
                        balance_pay_amount: isCombinationPay ? parseFloat(order.balance_pay_amount) : 0,
                        pay_id: orderInfo.transaction_id,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: isCombinationPay ? '微信组合支付成功' : '微信支付成功'
                        })
                    })
                );

                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);

                // 支付成功后处理优惠券：将优惠券状态设置为已使用
                if (order.coupon_code) {
                    try {
                        const couponService = this.service('coupon');
                        const useResult = await couponService.useCoupon(order.coupon_code, order.user_id, order.id);
                        if (!useResult) {
                            think.logger.error(`微信支付成功后使用优惠券失败，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                        } else {
                            think.logger.info(`微信支付成功后使用优惠券成功，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                        }
                    } catch (couponError) {
                        think.logger.error(`微信支付成功后使用优惠券异常，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}:`, couponError.message);
                    }
                }

                think.logger.info(`订单 ${order.order_sn} 状态已更新为已支付`);

                // 6. 批量更新库存和销量（并发执行）
                const stockUpdatePromises = [];
                for (const item of orderGoodsList) {
                    const { goods_id, product_id, number } = item;

                    // 减商品总库存
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).decrement('goods_number', number)
                    );

                    // 加销量
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).increment('sell_volume', number)
                    );

                    // 减具体 SKU 库存
                    stockUpdatePromises.push(
                        productModel.where({ id: product_id }).decrement('goods_number', number)
                    );
                }

                // 并发执行库存更新
                await Promise.all(stockUpdatePromises);

                think.logger.info(`订单 ${orderInfo.out_trade_no} 支付完成，微信交易信息：`, orderInfo);
                think.logger.info(`订单 ${order.order_sn} 商品库存和销量已更新`);

                // 发送支付成功推送通知给管理员
                try {
                    const pushService = think.service('push', 'common');
                    await pushService.sendOrderPaymentNotification(order, payType);
                } catch (pushError) {
                    think.logger.error('发送微信支付成功推送通知失败:', pushError);
                }

                return true;
            });

            return true;
        } catch (error) {
            think.logger.error('处理微信支付回调时出错:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return false; // 返回 false 而不是抛出异常
        }
    }

    async afterWxPayRefund(orderInfo) {
        try {
            await this.transaction('order', async (session) => {
                // 查出订单最新信息
                const order = await this.model('order').db(session).where({
                    pay_trade_no: orderInfo.out_trade_no
                }).find();

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单: ${orderInfo.out_trade_no}`);
                    return true;
                }

                // 计算退款金额和更新已退款总额
                const refundAmount = parseFloat(orderInfo.amount.refund) / 100; // 微信金额单位是分，需要转换为元
                const currentRefundedPrice = parseFloat(order.refunded_price || 0);
                const newRefundedPrice = currentRefundedPrice + refundAmount;

                // 判断是否为全额退款 - 考虑组合支付场景
                let isFullRefund;
                if (order.pay_type && order.pay_type.includes('+bal')) {
                    // 组合支付：判断是否退完了第三方支付部分
                    const primaryPayAmount = parseFloat(order.primary_pay_amount || 0);
                    isFullRefund = refundAmount >= primaryPayAmount;
                } else {
                    // 非组合支付：按原逻辑判断
                    isFullRefund = newRefundedPrice >= parseFloat(order.actual_price);
                }

                // 判断是否为组合支付余额不足的自动退款
                const isBalanceInsufficientRefund = orderInfo.out_refund_no && orderInfo.out_refund_no.includes('notBal');
                
                // 设置订单退款状态
                let updateInfo = {
                    order_status: (isFullRefund ? 203 : 206), // 余额不足自动退款：已取消，其他：全额退款或部分退款
                    refund_time: parseInt(new Date().getTime() / 1000),
                    refund_id: orderInfo.transaction_id,
                    refunded_price: newRefundedPrice
                };

                // 如果是余额不足自动退款，设置退款原因
                if (isBalanceInsufficientRefund) {
                    updateInfo.refund_reason = '组合支付余额不足自动退款';
                }

                // 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];

                // 发起成功之后将订单状态修改为已退款
                updatePromises.push(
                    this.model('order').db(session).where({
                        pay_trade_no: orderInfo.out_trade_no
                    }).update(updateInfo)
                );

                // 添加交易记录
                const transactionMessage = isBalanceInsufficientRefund ?
                    '微信组合支付余额不足自动退款成功' :
                    '微信退款成功';

                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        order_sn: order.order_sn,
                        pay_trade_no: order.pay_trade_no, // 新增：关联原支付流水号
                        user_id: order.user_id,
                        log_type: 'RF', // RF: 退款
                        pay_status: updateInfo.order_status,
                        pay_type: order.pay_type || 'wx', // 使用订单的实际支付方式
                        amount: refundAmount,
                        balance_pay_amount: order.balance_pay_amount || 0,
                        pay_id: orderInfo.transaction_id,
                        refund_id: orderInfo.refund_id,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: transactionMessage
                        })
                    })
                );

                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);

                return true;
            });

            return true;
        } catch (error) {
            think.logger.error('处理微信退款回调时出错:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return false; // 返回 false 而不是抛出异常
        }
    }

    /**
     * 支付宝支付
     */
    async aliPayAction() {
        const order_sn = this.post('orderSn');
        const orderId = this.post('orderId');
        const apiVersion = this.post('apiVersion');

        if (!orderId || !order_sn) {
            return this.fail(-1, '参数错误');
        }
        const checkPre = await this.prePay();
        if (checkPre) {
            return this.fail(400, checkPre);
        }

        // order_price 查出该订单的实际需要支付金额
        let orderWhere = {}
        if (orderId) {
            orderWhere.id = orderId;
        }
        if (order_sn) {
            orderWhere.order_sn = order_sn;
        }
        const orderInfo = await this.model('order').where(orderWhere).find();
        if (think.isEmpty(orderInfo)) {
            return this.fail(-1, '订单不存在');
        }
        // 生产环境必须是订单数据查询得到金额，测试环境可以是传入金额
        const order_price = isProd ? (orderInfo.actual_price || 0) : (this.post("orderPrice") || 0);

        if (order_price <= 0) {
            return this.fail(-1, '订单金额有误');
        }

        // 生成支付流水号，避免金额变化时的订单号重复问题
        const randomSuffix = Math.random().toString(36).substr(2, 5);
        const pay_trade_no = `${order_sn}-ALI-${randomSuffix}`;

        try {
            const result = await alipaySdk.sdkExec('alipay.trade.app.pay', {
                notify_url: alipay_notify_url, // 通知回调地址
                bizContent: {
                    out_trade_no: pay_trade_no, // 使用支付流水号而不是订单号
                    total_amount: order_price,
                    subject: '唯优众宠订单',
                }
            });

            think.logger.info('支付宝支付', result);

            if (think.isEmpty(result)) {
                return this.fail('支付失败');
            }

            // 记录支付流水号到订单表
            await this.model('order').where({ id: orderId }).update({
                pay_trade_no: pay_trade_no,
                pay_type: 'ali'
            });

            // 添加交易记录 - 发起支付
            await this.model('transaction_log').add({
                order_id: orderId,
                order_sn: order_sn,
                pay_trade_no: pay_trade_no,
                user_id: orderInfo.user_id,
                log_type: 'CS', // CS: 消费
                pay_status: 101, // 未付款
                pay_type: 'ali',
                amount: order_price,
                request_data: JSON.stringify({
                    order_sn: order_sn,
                    order_price: order_price,
                    pay_trade_no: pay_trade_no,
                    subject: '唯优众宠订单'
                }),
                response_data: JSON.stringify({
                    success: true,
                    message: '支付宝支付下单成功',
                    alipay_order_info: result
                })
            });

            // 返回支付参数，同时返回支付流水号供前端使用
            if (apiVersion == '1.1.1') {
                return this.success({
                    aliPayInfo: result,
                    pay_trade_no: pay_trade_no,
                    order_sn: order_sn,
                    order_price: order_price
                });
            } else {
                return this.success(result);
            }
        } catch (error) {
            think.logger.error('支付宝支付下单失败:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return this.fail(400, error.message || '支付宝支付下单失败');
        }
    }
    async aliPayNotifyAction() {
        // 获取原始请求数据，支付宝回调使用 application/x-www-form-urlencoded 格式
        const orderInfo = this.ctx.request.body;
        try {
            // 验证签名
            const signVerified = await alipaySdk.checkNotifySign(orderInfo);

            if (!signVerified) {
                think.logger.error('支付宝回调签名验证失败');
                // 按照支付宝的要求，需要返回 "success" 字符串
                this.ctx.type = 'text/plain';
                this.ctx.body = 'success';
                return;
            }

            // 处理交易成功的情况
            if (orderInfo.trade_status === 'TRADE_SUCCESS') {
                // 判断是否为充值订单
                if (orderInfo.out_trade_no.startsWith('RC')) {
                    const recharge = await this.afterRechargePayment(orderInfo);
                    if (!recharge) {
                        think.logger.error('支付宝充值回调处理失败，但返回success避免重复回调');
                        // 按照支付宝的要求，需要返回 "success" 字符串
                        this.ctx.type = 'text/plain';
                        this.ctx.body = 'success';
                        return;
                    }
                } else {
                    const order = await this.afterAliPay(orderInfo);
                    if (!order) {
                        think.logger.error('支付宝订单回调处理失败，但返回success避免重复回调');
                        // 按照支付宝的要求，需要返回 "success" 字符串
                        this.ctx.type = 'text/plain';
                        this.ctx.body = 'success';
                        return;
                    }
                }
            } else if (orderInfo.trade_status === 'TRADE_CLOSED') {
                if (orderInfo.out_trade_no.startsWith('RC')) {
                    //todo 处理充值退款，如果需要的话
                } else {
                    const refund = await this.aliPayRefund(orderInfo);
                    if (!refund) {
                        think.logger.error('支付宝退款回调处理失败，但返回success避免重复回调');
                        // 按照支付宝的要求，需要返回 "success" 字符串
                        this.ctx.type = 'text/plain';
                        this.ctx.body = 'success';
                        return;
                    }
                }
            }

            // 按照支付宝的要求，需要返回 "success" 字符串
            this.ctx.type = 'text/plain';
            this.ctx.body = 'success';
            return;
        } catch (error) {
            think.logger.error('处理支付宝回调时发生错误:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            think.logger.error('回调数据:', JSON.stringify(orderInfo));
            // 即使发生异常，也要按照支付宝的要求返回 "success" 字符串
            // 避免支付宝重复发送回调
            this.ctx.type = 'text/plain';
            this.ctx.body = 'success';
            return;
        }
    }

    async afterAliPay(orderInfo) {
        try {
            await this.transaction("order", async session => {
                const orderModel = this.model('order').db(session);
                const orderGoodsModel = this.model('order_goods').db(session);
                const goodsModel = this.model('goods').db(session);
                const productModel = this.model('product').db(session);

                // 记录支付流水号
                const payTradeNo = orderInfo.out_trade_no;
                think.logger.info(`处理支付宝回调，支付流水号: ${payTradeNo}`);

                // 1. 查找订单 - 现在使用支付流水号查找
                let order = await orderModel.where({
                    pay_trade_no: payTradeNo,
                    is_delete: 0
                }).find();

                // 兼容旧的订单号查找方式（向后兼容）
                if (think.isEmpty(order)) {
                    order = await orderModel.where({
                        order_sn: payTradeNo,
                        is_delete: 0
                    }).find();
                }

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单，支付流水号: ${payTradeNo}`);
                    return true;
                }

                // 2. 幂等判断：如果已支付，不重复处理
                if (order.order_status >= 201) {
                    think.logger.info(`订单 ${order.order_sn} 已经处理过支付，当前状态: ${order.order_status}`);
                    return true;
                }

                // 3. 判断是否为组合支付
                const isCombinationPay = payTradeNo.includes('-ALIBAL-');
                let payType = 'ali';
                let actualPrice = parseFloat(orderInfo.total_amount);

                if (isCombinationPay) {
                    payType = 'ali+bal';
                    actualPrice = parseFloat(order.actual_price); // 使用订单原始金额

                    // 组合支付：扣减用户余额
                    const balanceAmount = parseFloat(order.balance_pay_amount || 0);
                    if (balanceAmount > 0) {
                        const user = await this.model('user').db(session).where({ id: order.user_id }).find();
                        const currentBalance = parseFloat(user.balance || 0);

                        if (currentBalance < balanceAmount) {
                            // 余额不足，发送短信通知并发起退款
                            await this.sendBalanceInsufficientSMS(order.user_id, order.order_sn, 'ali+bal');
                            think.logger.error(`组合支付余额不足，订单：${order.order_sn}，需要：${balanceAmount}，当前：${currentBalance}`);

                            // 发起支付宝退款
                            await this.processBalanceInsufficientRefund(order, orderInfo, 'ali');
                            return true; // 返回成功避免重复回调，但订单状态不会更新为已支付
                        }

                        const newBalance = currentBalance - balanceAmount;

                        // 扣减用户余额
                        await this.model('user').db(session).where({ id: order.user_id }).update({
                            balance: newBalance
                        });

                        // 添加余额变动记录
                        await this.model('balance_log').db(session).add({
                            user_id: order.user_id,
                            amount: balanceAmount,
                            balance: newBalance,
                            log_type: 'CS',
                            related_pay_type: 'ali',
                            order_id: order.id,
                            order_sn: order.order_sn,
                            remark: `组合支付-余额部分：${order.order_sn}`
                        });
                    }
                }

                // 4. 获取订单商品明细（提前获取，减少事务内查询）
                const orderGoodsList = await orderGoodsModel.where({ order_id: order.id }).select();

                // 5. 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];

                // 更新订单状态为已支付
                updatePromises.push(
                    orderModel.where({ id: order.id }).update({
                        order_status: 201, // 已付款
                        pay_type: payType,
                        pay_time: Math.floor(Date.now() / 1000),
                        pay_id: orderInfo.trade_no,
                        actual_price: actualPrice
                    })
                );

                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        order_sn: order.order_sn,
                        pay_trade_no: payTradeNo,
                        user_id: order.user_id,
                        log_type: 'CS', // CS: 消费
                        pay_status: 201, // 已付款
                        pay_type: payType,
                        amount: isCombinationPay ? parseFloat(orderInfo.total_amount) : parseFloat(orderInfo.total_amount),
                        balance_pay_amount: isCombinationPay ? parseFloat(order.balance_pay_amount) : 0,
                        pay_id: orderInfo.trade_no,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: isCombinationPay ? '支付宝组合支付成功' : '支付宝支付成功'
                        })
                    })
                );

                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);

                // 支付成功后处理优惠券：将优惠券状态设置为已使用
                if (order.coupon_code) {
                    try {
                        const couponService = this.service('coupon');
                        const useResult = await couponService.useCoupon(order.coupon_code, order.user_id, order.id);
                        if (!useResult) {
                            think.logger.error(`支付宝支付成功后使用优惠券失败，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                        } else {
                            think.logger.info(`支付宝支付成功后使用优惠券成功，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                        }
                    } catch (couponError) {
                        think.logger.error(`支付宝支付成功后使用优惠券异常，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}:`, couponError.message);
                    }
                }

                think.logger.info(`订单 ${order.order_sn} 状态已更新为已支付`);

                // 6. 批量更新库存和销量（并发执行）
                const stockUpdatePromises = [];
                for (const item of orderGoodsList) {
                    const { goods_id, product_id, number } = item;

                    // 减商品总库存
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).decrement('goods_number', number)
                    );

                    // 加销量
                    stockUpdatePromises.push(
                        goodsModel.where({ id: goods_id }).increment('sell_volume', number)
                    );

                    // 减具体 SKU 库存
                    stockUpdatePromises.push(
                        productModel.where({ id: product_id }).decrement('goods_number', number)
                    );
                }

                // 并发执行库存更新
                await Promise.all(stockUpdatePromises);

                think.logger.info(`订单 ${orderInfo.out_trade_no} 支付完成，支付宝交易信息：`, orderInfo);
                think.logger.info(`订单 ${order.order_sn} 商品库存和销量已更新`);

                // 发送支付成功推送通知给管理员
                try {
                    const pushService = think.service('push', 'common');
                    await pushService.sendOrderPaymentNotification(order, payType);
                } catch (pushError) {
                    think.logger.error('发送支付宝支付成功推送通知失败:', pushError);
                }

                return true;
            });
            return true; // 成功处理
        } catch (error) {
            think.logger.error('处理支付宝支付回调时出错:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return false; // 返回 false 而不是抛出异常
        }
    }

    async aliPayRefund(orderInfo) {
        try {
            await this.transaction('order', async (session) => {
                // 查出订单最新信息
                const order = await this.model('order').db(session).where({
                    pay_trade_no: orderInfo.out_trade_no
                }).find();

                if (think.isEmpty(order)) {
                    think.logger.error(`找不到订单: ${orderInfo.out_trade_no}`);
                    return true;
                }

                // 计算退款金额和更新已退款总额
                const refundAmount = parseFloat(orderInfo.refund_fee); // 支付宝金额单位是元
                const currentRefundedPrice = parseFloat(order.refunded_price || 0);
                const newRefundedPrice = currentRefundedPrice + refundAmount;

                // 判断是否为全额退款 - 考虑组合支付场景
                let isFullRefund;
                if (order.pay_type && order.pay_type.includes('+bal')) {
                    // 组合支付：判断是否退完了第三方支付部分
                    const primaryPayAmount = parseFloat(order.primary_pay_amount || 0);
                    isFullRefund = refundAmount >= primaryPayAmount;
                } else {
                    // 非组合支付：按原逻辑判断
                    isFullRefund = newRefundedPrice >= parseFloat(order.actual_price);
                }

                // 判断是否为组合支付余额不足的自动退款
                const isBalanceInsufficientRefund = orderInfo.out_request_no && orderInfo.out_request_no.includes('notBal');

                // 设置订单退款状态
                let updateInfo = {
                    order_status: (isFullRefund ? 203 : 206), // 余额不足自动退款：已取消，其他：全额退款或部分退款
                    refund_time: parseInt(new Date().getTime() / 1000),
                    refund_id: orderInfo.trade_no,
                    refunded_price: newRefundedPrice
                };

                // 如果是余额不足自动退款，设置退款原因
                if (isBalanceInsufficientRefund) {
                    updateInfo.refund_reason = '组合支付余额不足自动退款';
                }

                // 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];

                // 发起成功之后将订单状态修改为已退款
                updatePromises.push(
                    this.model('order').db(session).where({
                        pay_trade_no: orderInfo.out_trade_no
                    }).update(updateInfo)
                );

                // 添加交易记录
                const transactionMessage = isBalanceInsufficientRefund ?
                    '支付宝组合支付余额不足自动退款成功' :
                    '支付宝退款成功';

                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        order_sn: order.order_sn,
                        pay_trade_no: order.pay_trade_no, // 新增：关联原支付流水号
                        user_id: order.user_id,
                        log_type: 'RF', // RF: 退款
                        pay_status: updateInfo.order_status,
                        pay_type: order.pay_type || 'ali', // 使用订单的实际支付方式
                        amount: orderInfo.refund_fee,
                        balance_pay_amount: order.balance_pay_amount || 0,
                        pay_id: orderInfo.trade_no,
                        refund_id: orderInfo.refund_id,
                        request_data: JSON.stringify(orderInfo),
                        response_data: JSON.stringify({
                            success: true,
                            message: transactionMessage
                        })
                    })
                );

                // 并发执行订单状态更新和交易记录
                await Promise.all(updatePromises);
                return true
            });
            return true; // 成功处理
        } catch (error) {
            think.logger.error('处理支付宝退款回调时出错:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return false; // 返回 false 而不是抛出异常
        }
    }

    /**
     * 支付宝充值支付
     */
    async aliPayRechargeAction() {
        const rechargeNo = this.get('rechargeNo');
        const amount = this.get('amount');

        if (!rechargeNo || !amount) {
            return this.fail(-1, '参数错误');
        }

        const result = await alipaySdk.sdkExec('alipay.trade.app.pay', {
            notify_url: alipay_notify_url, // 通知回调地址
            bizContent: {
                out_trade_no: rechargeNo,
                total_amount: amount,
                subject: '唯优众宠充值',
            }
        });

        think.logger.info('支付宝充值', result);
        if (think.isEmpty(result)) {
            return this.fail('支付失败');
        }
        return this.success({
            aliPayment: result,
            rechargeNo: rechargeNo,
            amount: amount
        });
    }

    /**
     * 微信充值支付
     */
    async weixinPayRechargeAction() {
        const rechargeNo = this.get('rechargeNo');
        const amount = this.get('amount');

        if (!rechargeNo || !amount) {
            return this.fail(-1, '参数错误');
        }

        const result = await wechatpay.transactions_app({
            appid: 'wxf54e017f3b833a82',
            description: '唯优众宠充值',
            out_trade_no: rechargeNo,
            amount: {
                total: parseFloat(amount) * 100
            },
            notify_url: wxpay_notify_url
        });

        think.logger.info('微信充值下单结果：', result);
        if (result.status != 200) {
            return this.fail(400, result.error);
        }
        return this.success({
            wxPayment: result.data,
            rechargeNo: rechargeNo,
            amount: amount
        });
    }

    /**
     * 处理充值支付回调
     */
    async afterRechargePayment(paymentInfo) {
        const maxRetries = 3;
        let retryCount = 0;

        while (retryCount < maxRetries) {
            try {
                // 先检查是否已经处理过（幂等性检查）
                const existingRecharge = await this.model('recharge').where({
                    recharge_no: paymentInfo.out_trade_no,
                    pay_status: 201 // 已付款状态
                }).find();

                if (!think.isEmpty(existingRecharge)) {
                    think.logger.info(`充值订单 ${paymentInfo.out_trade_no} 已经处理过`);
                    return true;
                }

                await this.transaction('recharge', async (session) => {
                    think.logger.info(`开始处理充值回调，充值单号: ${paymentInfo.out_trade_no}`);

                    // 查询充值记录和用户信息（一次性查询，减少数据库交互）
                    const recharge = await this.model('recharge').db(session).where({
                        recharge_no: paymentInfo.out_trade_no,
                        pay_status: 101 // 未付款状态
                    }).find();

                    think.logger.info(`查询充值记录结果:`, JSON.stringify(recharge));

                    if (think.isEmpty(recharge)) {
                        think.logger.info(`充值订单 ${paymentInfo.out_trade_no} 不存在或已处理`);
                        return true;
                    }

                    // 查询用户信息
                    const user = await this.model('user').db(session).where({
                        id: recharge.user_id
                    }).find();

                    if (think.isEmpty(user)) {
                        think.logger.error(`用户 ${recharge.user_id} 不存在`);
                        return true;
                    }

                    // 计算赠送金额
                    const rechargeAmount = parseFloat(recharge.amount);
                    let bonusAmount = 0;
                    let remarkBonus = '';

                    // 根据用户类型和充值金额计算赠送金额
                    if (user.is_agent === 1) {
                        // 代理商充值优惠规则
                        if (rechargeAmount === 5000) {
                            bonusAmount = 30;
                            remarkBonus = '门店医院充值5000元赠送30元';
                        } else if (rechargeAmount === 10000) {
                            bonusAmount = 60;
                            remarkBonus = '门店医院充值10000元赠送60元';
                        }
                    } else {
                        // 普通用户充值优惠规则
                        if (rechargeAmount === 500) {
                            bonusAmount = 20;
                            remarkBonus = '会员充值500元赠送20元';
                        } else if (rechargeAmount === 1000) {
                            bonusAmount = 50;
                            remarkBonus = '会员充值1000元赠送50元';
                        } else if (rechargeAmount === 3000) {
                            bonusAmount = 120;
                            remarkBonus = '会员充值3000元赠送120元';
                        } else if (rechargeAmount === 5000) {
                            bonusAmount = 200;
                            remarkBonus = '会员充值5000元赠送200元';
                        } else if (rechargeAmount === 10000) {
                            bonusAmount = 300;
                            remarkBonus = '会员充值10000元赠送300元';
                        }
                    }

                    // 计算最终到账金额
                    const currentBalance = parseFloat(user.balance || 0);
                    const totalAmount = rechargeAmount + bonusAmount;
                    const newBalance = currentBalance + totalAmount;

                    // 批量更新操作（减少数据库交互次数）
                    const updatePromises = [];

                    // 1. 更新充值记录状态
                    updatePromises.push(
                        this.model('recharge').db(session).where({
                            id: recharge.id
                        }).update({
                            pay_status: 201, // 已付款
                            pay_time: parseInt(new Date().getTime() / 1000),
                            pay_id: paymentInfo.transaction_id || paymentInfo.trade_no
                        })
                    );

                    // 2. 更新用户余额
                    updatePromises.push(
                        this.model('user').db(session).where({
                            id: recharge.user_id
                        }).update({
                            balance: newBalance
                        })
                    );

                    // 3. 添加余额变动记录
                    const payTypeText = paymentInfo.transaction_id ? '微信支付' : '支付宝支付';
                    const payType = paymentInfo.transaction_id ? 'wx' : 'ali';
                    const remarkText = `账户充值：${recharge.amount}元，支付方式：${payTypeText}，充值单号：${recharge.recharge_no}，${remarkBonus}`;

                    updatePromises.push(
                        this.model('balance_log').db(session).add({
                            user_id: recharge.user_id,
                            amount: totalAmount,
                            balance: newBalance,
                            log_type: 'RC', // RC: 充值
                            order_sn: recharge.recharge_no,
                            order_id: recharge.id,
                            remark: remarkText
                        })
                    );

                    // 4. 添加交易记录
                    updatePromises.push(
                        this.model('transaction_log').db(session).add({
                            order_id: recharge.id, // 使用充值记录ID
                            order_sn: recharge.recharge_no,
                            user_id: recharge.user_id,
                            log_type: 'RC', // RC: 充值
                            pay_status: 201, // 已付款
                            pay_type: payType,
                            amount: recharge.amount,
                            pay_id: paymentInfo.transaction_id || paymentInfo.trade_no,
                            request_data: JSON.stringify(paymentInfo),
                            response_data: JSON.stringify({
                                success: true,
                                message: '充值成功'
                            })
                        })
                    );

                    // 并发执行所有更新操作
                    think.logger.info(`准备执行 ${updatePromises.length} 个数据库更新操作`);
                    await Promise.all(updatePromises);
                    think.logger.info(`所有数据库更新操作执行成功`);

                    think.logger.info(`用户 ${recharge.user_id} 充值 ${recharge.amount} 元成功，赠送 ${bonusAmount} 元，当前余额 ${newBalance} 元`);
                    return true;
                });

                return true;
            } catch (error) {
                retryCount++;
                think.logger.error(`处理充值回调时出错 (第${retryCount}次重试):`, error.message);
                think.logger.error('错误堆栈:', error.stack);
                think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));

                if (retryCount >= maxRetries) {
                    think.logger.error(`充值处理失败，已重试${maxRetries}次，充值单号: ${paymentInfo.out_trade_no}`);
                    return false;
                }

                // 等待一段时间后重试
                await new Promise(resolve => setTimeout(resolve, 1000 * retryCount));
            }
        }

        return false;
    }

    /**
     * 余额支付
     */
    async balancePayAction() {
        const order_sn = this.post('orderSn');
        const orderId = this.post('orderId');

        if (!orderId || !order_sn) {
            return this.fail(-1, '参数错误');
        }

        // order_price 查出该订单的实际需要支付金额
        let orderWhere = {}
        if (orderId) {
            orderWhere.id = orderId;
        }
        if (order_sn) {
            orderWhere.order_sn = order_sn;
        }
        const orderInfo = await this.model('order').where(orderWhere).find();

        // 生产环境必须是订单数据查询得到金额，测试环境可以是传入金额
        const order_price = isProd ? (orderInfo.actual_price || 0) : (this.post("orderPrice") || 0);

        const checkPre = await this.prePay();
        if (checkPre) {
            return this.fail(400, checkPre);
        }

        try {
            await this.transaction("order", async session => {
                const userId = this.getLoginUserId();

                // 1. 查询用户余额
                const user = await this.model('user').db(session).where({
                    id: userId
                }).find();

                const balance = parseFloat(user.balance || 0);
                const orderAmount = parseFloat(order_price);

                // 2. 检查余额是否足够
                if (balance < orderAmount) {
                    throw new Error('余额不足，请充值');
                }

                // 3. 查找订单
                const order = await this.model('order').db(session).where({
                    id: orderId,
                    order_sn: order_sn,
                    is_delete: 0
                }).find();

                if (think.isEmpty(order)) {
                    throw new Error('订单不存在');
                }

                // 4. 幂等判断：如果已支付，不重复处理
                if (order.order_status >= 201) {
                    throw new Error('订单已支付');
                }

                // 5. 生成余额支付流水号
                const randomSuffix = Math.random().toString(36).substr(2, 5);
                const pay_trade_no = `${order_sn}-BAL-${randomSuffix}`;

                // 6. 获取订单商品明细（提前获取，减少事务内查询）
                const orderGoodsList = await this.model('order_goods').db(session).where({
                    order_id: order.id
                }).select();

                // 7. 批量更新操作（使用 Promise.all 并发执行）
                const updatePromises = [];

                // 扣减用户余额
                const newBalance = balance - orderAmount;
                updatePromises.push(
                    this.model('user').db(session).where({
                        id: userId
                    }).update({
                        balance: newBalance
                    })
                );

                // 更新订单状态为已支付
                updatePromises.push(
                    this.model('order').db(session).where({ id: order.id }).update({
                        order_status: 201, // 已付款
                        pay_type: 'balance',
                        pay_time: Math.floor(Date.now() / 1000),
                        actual_price: orderAmount,
                        pay_trade_no: pay_trade_no // 记录余额支付流水号
                    })
                );

                // 添加余额变动记录
                updatePromises.push(
                    this.model('balance_log').db(session).add({
                        user_id: userId,
                        amount: orderAmount,
                        balance: newBalance,
                        log_type: 'CS', // CS: 消费
                        order_sn: order.order_sn,
                        order_id: order.id,
                        remark: `订单支付：${order_sn}`
                    })
                );

                // 添加交易记录
                updatePromises.push(
                    this.model('transaction_log').db(session).add({
                        order_id: order.id,
                        order_sn: order.order_sn,
                        pay_trade_no: pay_trade_no, // 新增：记录余额支付流水号
                        user_id: userId,
                        log_type: 'CS', // CS: 消费
                        pay_status: 201, // 已付款
                        pay_type: 'balance',
                        amount: orderAmount,
                        pay_id: pay_trade_no, // 余额支付使用流水号作为支付ID
                        request_data: JSON.stringify({
                            order_sn: order_sn,
                            order_price: orderAmount,
                            pay_trade_no: pay_trade_no
                        }),
                        response_data: JSON.stringify({
                            success: true,
                            message: '余额支付成功'
                        })
                    })
                );

                // 并发执行所有更新操作
                await Promise.all(updatePromises);

                // 支付成功后处理优惠券：将优惠券状态设置为已使用
                if (order.coupon_code) {
                    try {
                        const couponService = this.service('coupon');
                        const useResult = await couponService.useCoupon(order.coupon_code, order.user_id, order.id);
                        if (!useResult) {
                            think.logger.error(`余额支付成功后使用优惠券失败，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                        } else {
                            think.logger.info(`余额支付成功后使用优惠券成功，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}`);
                        }
                    } catch (couponError) {
                        think.logger.error(`余额支付成功后使用优惠券异常，订单ID: ${order.id}, 优惠券码: ${order.coupon_code}:`, couponError.message);
                    }
                }

                // 7. 批量更新商品库存和销量（并发执行）
                const stockUpdatePromises = [];
                for (const item of orderGoodsList) {
                    const { goods_id, product_id, number } = item;

                    // 减商品总库存
                    stockUpdatePromises.push(
                        this.model('goods').db(session).where({ id: goods_id }).decrement('goods_number', number)
                    );

                    // 加销量
                    stockUpdatePromises.push(
                        this.model('goods').db(session).where({ id: goods_id }).increment('sell_volume', number)
                    );

                    // 减具体 SKU 库存
                    stockUpdatePromises.push(
                        this.model('product').db(session).where({ id: product_id }).decrement('goods_number', number)
                    );
                }

                // 并发执行库存更新
                await Promise.all(stockUpdatePromises);

                think.logger.info(`订单 ${order_sn} 余额支付成功，金额: ${orderAmount}，用户余额: ${newBalance}`);

                // 发送支付成功推送通知给管理员
                try {
                    const pushService = think.service('push', 'common');
                    await pushService.sendOrderPaymentNotification(order, 'balance');
                } catch (pushError) {
                    think.logger.error('发送余额支付成功推送通知失败:', pushError);
                }

                return true;
            });

            return this.success({
                message: '支付成功'
            });
        } catch (error) {
            think.logger.error('余额支付失败:', error.message);
            think.logger.error('错误堆栈:', error.stack);
            think.logger.error('错误详情:', JSON.stringify(error, Object.getOwnPropertyNames(error)));
            return this.fail(400, error.message || '支付失败');
        }
    }

    /**
     * 智能组合支付 - 余额+微信
     * 根据用户余额自动选择最优支付方式：
     * 1. 余额为0 -> 调用纯微信支付接口
     * 2. 余额足够 -> 调用纯余额支付接口
     * 3. 余额不足 -> 使用组合支付
     */
    async combinationWxPayAction() {
        const order_sn = this.post('orderSn');
        const orderId = this.post('orderId');
        const requestBalanceAmount = parseFloat(this.post('balanceAmount') || 0);
        const apiVersion = this.post('apiVersion');

        think.logger.info(`[组合支付-微信] 开始处理订单 ${order_sn}，请求余额支付金额: ${requestBalanceAmount}`);

        if (!orderId || !order_sn) {
            think.logger.error(`[组合支付-微信] 参数错误: orderId=${orderId}, order_sn=${order_sn}`);
            return this.fail(-1, '参数错误');
        }

        // 获取订单信息
        const orderInfo = await this.model('order').where({ id: orderId }).find();
        if (think.isEmpty(orderInfo)) {
            think.logger.error(`[组合支付-微信] 订单不存在: ${orderId}`);
            return this.fail(-1, '订单不存在');
        }

        const order_price = isProd ? (orderInfo.actual_price || 0) : (this.post("orderPrice") || 0);
        if (order_price <= 0) {
            think.logger.error(`[组合支付-微信] 订单金额有误: ${order_price}`);
            return this.fail(-1, '订单金额有误');
        }

        // 获取用户当前余额
        const userId = this.getLoginUserId();
        const user = await this.model('user').where({ id: userId }).find();
        const userBalance = parseFloat(user.balance || 0);

        think.logger.info(`[组合支付-微信] 订单金额: ${order_price}, 用户余额: ${userBalance}, 请求余额支付: ${requestBalanceAmount}`);

        try {
            // 智能判断支付方式
            if (userBalance <= 0) {
                // 情况1: 余额为0，调用纯微信支付接口
                think.logger.info(`[组合支付-微信] 用户余额为0，转为纯微信支付，调用 weixinPayAction`);
                return await this.weixinPayAction();

            } else if (userBalance >= order_price) {
                // 情况2: 余额足够，调用纯余额支付接口
                think.logger.info(`[组合支付-微信] 用户余额充足(${userBalance} >= ${order_price})，转为纯余额支付，调用 balancePayAction`);
                return await this.balancePayAction();

            } else {
                // 情况3: 余额不足，使用组合支付
                const actualBalanceAmount = Math.min(requestBalanceAmount, userBalance);
                const wxPayAmount = order_price - actualBalanceAmount;

                think.logger.info(`[组合支付-微信] 使用组合支付，余额支付: ${actualBalanceAmount}, 微信支付: ${wxPayAmount}`);
                return await this.processCombinationWxPay(orderId, order_sn, order_price, actualBalanceAmount, wxPayAmount, orderInfo.user_id, apiVersion);
            }
        } catch (error) {
            think.logger.error(`[组合支付-微信] 处理失败:`, error.message);
            think.logger.error(`[组合支付-微信] 错误堆栈:`, error.stack);
            return this.fail(400, error.message || '支付处理失败');
        }
    }

    /**
     * 处理组合支付 - 微信
     */
    async processCombinationWxPay(orderId, order_sn, order_price, balanceAmount, wxPayAmount, user_id, apiVersion) {
        const randomSuffix = Math.random().toString(36).substr(2, 5);
        const pay_trade_no = `${order_sn}-WXBAL-${randomSuffix}`;

        think.logger.info(`[组合支付-微信] 生成支付流水号: ${pay_trade_no}`);

        // 更新订单支付信息
        await this.model('order').where({ id: orderId }).update({
            pay_trade_no: pay_trade_no,
            pay_type: 'wx+bal',
            balance_pay_amount: balanceAmount,
            primary_pay_amount: wxPayAmount
        });

        // 发起微信支付
        const result = await wechatpay.transactions_app({
            appid: 'wxf54e017f3b833a82',
            description: '唯优众宠订单',
            out_trade_no: pay_trade_no,
            amount: {
                total: Math.round(wxPayAmount * 100)
            },
            notify_url: wxpay_notify_url
        });

        think.logger.info(`[组合支付-微信] 微信下单结果:`, result);

        if (result.status != 200) {
            think.logger.error(`[组合支付-微信] 微信下单失败:`, result.error);
            return this.fail(400, result.error);
        }

        // 添加交易记录
        await this.model('transaction_log').add({
            order_id: orderId,
            order_sn: order_sn,
            pay_trade_no: pay_trade_no,
            user_id: user_id,
            log_type: 'CS',
            pay_status: 101,
            pay_type: 'wx+bal',
            amount: wxPayAmount,
            balance_pay_amount: balanceAmount,
            request_data: JSON.stringify({
                order_sn: order_sn,
                order_price: order_price,
                balance_amount: balanceAmount,
                wx_amount: wxPayAmount,
                pay_trade_no: pay_trade_no
            }),
            response_data: JSON.stringify({
                success: true,
                message: '组合支付-微信下单成功',
                prepay_id: result.data.prepay_id || ''
            })
        });

        think.logger.info(`[组合支付-微信] 支付处理完成，订单: ${order_sn}`);

        if (apiVersion == '1.1.1') {
            return this.success({
                wxPayInfo: result.data,
                pay_trade_no: pay_trade_no,
                order_sn: order_sn,
                order_price: order_price,
                balance_amount: balanceAmount,
                wx_amount: wxPayAmount,
                payment_type: 'combination'
            });
        } else {
            return this.success(result.data);
        }
    }

    /**
     * 智能组合支付 - 余额+支付宝
     * 根据用户余额自动选择最优支付方式：
     * 1. 余额为0 -> 调用纯支付宝支付接口
     * 2. 余额足够 -> 调用纯余额支付接口
     * 3. 余额不足 -> 使用组合支付
     */
    async combinationAliPayAction() {
        const order_sn = this.post('orderSn');
        const orderId = this.post('orderId');
        const requestBalanceAmount = parseFloat(this.post('balanceAmount') || 0);
        const apiVersion = this.post('apiVersion');

        think.logger.info(`[组合支付-支付宝] 开始处理订单 ${order_sn}，请求余额支付金额: ${requestBalanceAmount}`);

        if (!orderId || !order_sn) {
            think.logger.error(`[组合支付-支付宝] 参数错误: orderId=${orderId}, order_sn=${order_sn}`);
            return this.fail(-1, '参数错误');
        }

        // 获取订单信息
        const orderInfo = await this.model('order').where({ id: orderId }).find();
        if (think.isEmpty(orderInfo)) {
            think.logger.error(`[组合支付-支付宝] 订单不存在: ${orderId}`);
            return this.fail(-1, '订单不存在');
        }

        const order_price = isProd ? (orderInfo.actual_price || 0) : (this.post("orderPrice") || 0);
        if (order_price <= 0) {
            think.logger.error(`[组合支付-支付宝] 订单金额有误: ${order_price}`);
            return this.fail(-1, '订单金额有误');
        }

        // 获取用户当前余额
        const userId = this.getLoginUserId();
        const user = await this.model('user').where({ id: userId }).find();
        const userBalance = parseFloat(user.balance || 0);

        think.logger.info(`[组合支付-支付宝] 订单金额: ${order_price}, 用户余额: ${userBalance}, 请求余额支付: ${requestBalanceAmount}`);

        try {
            // 智能判断支付方式
            if (userBalance <= 0) {
                // 情况1: 余额为0，调用纯支付宝支付接口
                think.logger.info(`[组合支付-支付宝] 用户余额为0，转为纯支付宝支付，调用 aliPayAction`);
                return await this.aliPayAction();

            } else if (userBalance >= order_price) {
                // 情况2: 余额足够，调用纯余额支付接口
                think.logger.info(`[组合支付-支付宝] 用户余额充足(${userBalance} >= ${order_price})，转为纯余额支付，调用 balancePayAction`);
                return await this.balancePayAction();

            } else {
                // 情况3: 余额不足，使用组合支付
                const actualBalanceAmount = Math.min(requestBalanceAmount, userBalance);
                const aliPayAmount = order_price - actualBalanceAmount;

                think.logger.info(`[组合支付-支付宝] 使用组合支付，余额支付: ${actualBalanceAmount}, 支付宝支付: ${aliPayAmount}`);
                return await this.processCombinationAliPay(orderId, order_sn, order_price, actualBalanceAmount, aliPayAmount, orderInfo.user_id, apiVersion);
            }
        } catch (error) {
            think.logger.error(`[组合支付-支付宝] 处理失败:`, error.message);
            think.logger.error(`[组合支付-支付宝] 错误堆栈:`, error.stack);
            return this.fail(400, error.message || '支付处理失败');
        }
    }

    /**
     * 处理组合支付 - 支付宝
     */
    async processCombinationAliPay(orderId, order_sn, order_price, balanceAmount, aliPayAmount, user_id, apiVersion) {
        const randomSuffix = Math.random().toString(36).substr(2, 5);
        const pay_trade_no = `${order_sn}-ALIBAL-${randomSuffix}`;

        think.logger.info(`[组合支付-支付宝] 生成支付流水号: ${pay_trade_no}`);

        // 更新订单支付信息
        await this.model('order').where({ id: orderId }).update({
            pay_trade_no: pay_trade_no,
            pay_type: 'ali+bal',
            balance_pay_amount: balanceAmount,
            primary_pay_amount: aliPayAmount
        });

        // 发起支付宝支付
        const result = await alipaySdk.sdkExec('alipay.trade.app.pay', {
            notify_url: alipay_notify_url,
            bizContent: {
                out_trade_no: pay_trade_no,
                total_amount: aliPayAmount.toFixed(2),
                subject: '唯优众宠订单',
            }
        });

        think.logger.info(`[组合支付-支付宝] 支付宝下单结果:`, result);

        if (think.isEmpty(result)) {
            think.logger.error(`[组合支付-支付宝] 支付宝下单失败`);
            return this.fail('支付失败');
        }

        // 添加交易记录
        await this.model('transaction_log').add({
            order_id: orderId,
            order_sn: order_sn,
            pay_trade_no: pay_trade_no,
            user_id: user_id,
            log_type: 'CS',
            pay_status: 101,
            pay_type: 'ali+bal',
            amount: aliPayAmount,
            balance_pay_amount: balanceAmount,
            request_data: JSON.stringify({
                order_sn: order_sn,
                order_price: order_price,
                balance_amount: balanceAmount,
                ali_amount: aliPayAmount,
                pay_trade_no: pay_trade_no
            }),
            response_data: JSON.stringify({
                success: true,
                message: '组合支付-支付宝下单成功',
                alipay_order_info: result
            })
        });

        think.logger.info(`[组合支付-支付宝] 支付处理完成，订单: ${order_sn}`);

        if (apiVersion == '1.1.1') {
            return this.success({
                aliPayInfo: result,
                pay_trade_no: pay_trade_no,
                order_sn: order_sn,
                order_price: order_price,
                balance_amount: balanceAmount,
                ali_amount: aliPayAmount,
                payment_type: 'combination'
            });
        } else {
            return this.success(result);
        }
    }



    /**
     * 处理余额不足时的自动退款
     */
    async processBalanceInsufficientRefund(order, paymentInfo, payType) {
        try {
            const refundAmount = payType === 'wx' ?
                parseFloat(paymentInfo.amount.total) / 100 :
                parseFloat(paymentInfo.total_amount);

            think.logger.info(`开始处理余额不足退款，订单：${order.order_sn}，退款金额：${refundAmount}`);

            let refundResult = false;

            if (payType === 'wx') {
                // 微信退款
                refundResult = await wechatpay.refunds({
                    out_refund_no: 'wx-notBal-' + new Date().getTime() + '-' + order.order_sn,
                    out_trade_no: order.pay_trade_no,
                    notify_url: wxpay_refund_notify_url,
                    amount: {
                        refund: Math.round(refundAmount * 100),
                        total: Math.round(refundAmount * 100),
                        currency: 'CNY'
                    },
                    reason: '组合支付余额不足自动退款'
                });

                think.logger.info('余额不足微信自动退款结果：', JSON.stringify(refundResult));

                if (!think.isEmpty(refundResult) && refundResult.status === 200) {
                    // 更新订单状态为已取消
                    await this.model('order').where({ id: order.id }).update({
                        order_status: 203,
                        refund_reason: '组合支付余额不足自动退款',
                        refund_time: parseInt(new Date().getTime() / 1000)
                    });

                    // 记录交易日志
                    await this.model('transaction_log').add({
                        order_id: order.id,
                        order_sn: order.order_sn,
                        pay_trade_no: order.pay_trade_no,
                        user_id: order.user_id,
                        log_type: 'RF', // RF: 退款
                        pay_status: 203, // 已取消
                        pay_type: 'wx+bal',
                        amount: refundAmount,
                        request_data: JSON.stringify({
                            reason: '组合支付余额不足自动退款',
                            refund_amount: refundAmount
                        }),
                        response_data: JSON.stringify(refundResult)
                    });
                }
            } else if (payType === 'ali') {
                // 支付宝退款
                refundResult = await alipaySdk.exec('alipay.trade.refund', {
                    bizContent: {
                        out_trade_no: order.pay_trade_no,
                        refund_amount: refundAmount.toFixed(2),
                        refund_reason: "组合支付余额不足自动退款",
                        out_request_no: 'ali-notBal-' + new Date().getTime() + '-' + order.order_sn
                    }
                });

                think.logger.info('余额不足支付宝自动退款结果：', JSON.stringify(refundResult));

                if (!think.isEmpty(refundResult) && refundResult.code === '10000') {
                    // 更新订单状态为已取消
                    await this.model('order').where({ id: order.id }).update({
                        order_status: 203, // 已取消
                        refund_reason: '组合支付余额不足自动退款',
                        refund_time: parseInt(new Date().getTime() / 1000),
                        refunded_price: refundAmount
                    });

                    // 记录交易日志
                    await this.model('transaction_log').add({
                        order_id: order.id,
                        order_sn: order.order_sn,
                        pay_trade_no: order.pay_trade_no,
                        user_id: order.user_id,
                        log_type: 'RF', // RF: 退款
                        pay_status: 203, // 已取消
                        pay_type: 'ali+bal',
                        amount: refundAmount,
                        request_data: JSON.stringify({
                            reason: '组合支付余额不足自动退款',
                            refund_amount: refundAmount
                        }),
                        response_data: JSON.stringify(refundResult)
                    });
                }
            }

            think.logger.info(`订单 ${order.order_sn} 余额不足自动退款处理完成`);
        } catch (error) {
            think.logger.error('处理余额不足自动退款时出错:', error.message);
            think.logger.error('错误堆栈:', error.stack);
        }
    }

    /**
     * 发送余额不足短信通知
     */
    async sendBalanceInsufficientSMS(userId, orderSn, payType) {
        try {
            // 查询用户手机号
            const user = await this.model('user').where({ id: userId }).find();
            if (think.isEmpty(user) || !user.mobile) {
                think.logger.error(`用户 ${userId} 手机号为空，无法发送短信`);
                return;
            }

            // 确定支付方式文本
            const payTypeText = payType === 'wx+bal' ? '微信' : '支付宝';

            // 发送短信通知
            const orderService = think.service('order', 'admin');
            const smsError = await orderService.sendSMS(
                "2497661", // 需要配置短信模板ID
                [orderSn, payTypeText],
                [`+86${user.mobile}`]
            );

            if (smsError) {
                think.logger.error("发送余额不足短信通知失败：", smsError);
            } else {
                think.logger.info(`已发送余额不足短信通知给用户 ${userId}，订单：${orderSn}`);
            }
        } catch (error) {
            think.logger.error('发送余额不足短信通知异常:', error);
        }
    }

    /**
     * 查询充值结果
     */
    async rechargeStatusAction() {
        const userId = this.getLoginUserId();
        const rechargeNo = this.post('rechargeNo');
        const rechargeId = this.post('rechargeId');
        if (think.isEmpty(rechargeNo) && think.isEmpty(rechargeId)) {
            return this.fail('参数错误');
        }
        let where = {
            user_id: userId
        };
        if (!think.isEmpty(rechargeNo)) {
            where.recharge_no = rechargeNo;
        }
        if (!think.isEmpty(rechargeId)) {
            where.id = rechargeId;
        }
        const recharge = await this.model('recharge').where(where).find();
        return this.success({
            pay_status: recharge.pay_status
        });
    }
};
