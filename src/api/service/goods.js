/*
 * @Author: huaze.fan <EMAIL>
 * @Date: 2025-04-18 16:28:17
 * @LastEditors: <PERSON><PERSON> <EMAIL>
 * @LastEditTime: 2025-09-01 00:41:47
 * @FilePath: /petshop-server/src/api/service/goods.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// src/service/goods.js
const Base = require('../../common/service/base.js');
module.exports = class extends Base {
  /**
   * 商品库存校验 + 总价计算
   */
  async _checkGoodsAndCalcTotal(goodsList, goodsModel, userAgent) {
    const checkPudList = await goodsModel.checkProduct(goodsList);
    if (checkPudList.length !== goodsList.length) {
      throw this.throwError(-1, '部分商品已下架或库存不足');
    }

    let goodsTotalPrice = 0;
    for (const item of checkPudList) {
      const match = goodsList.find(g => g.goodsId == item.goods_id && g.productId == item.product_id);
      if (!match || !match.number) throw this.throwError(-1, '商品信息有误');

      const unitPrice = userAgent && !think.isEmpty(item.agent_price) ? item.agent_price : item.retail_price;
      goodsTotalPrice += parseFloat(unitPrice) * parseInt(match.number);
      item.buy_number = match.number;

      if (userAgent && item.agent_price) {
        item.retail_price = item.agent_price;
      }
    }

    return { checkPudList, goodsTotalPrice };
  }

  /**
   * 地址获取逻辑封装
   */
  async _getValidAddress(addressId, userId, addressModel) {
    let address = null;

    if (addressId) {
      address = await addressModel.where({ id: addressId, user_id: userId, is_delete: 0 }).find();
    } else {
      address = await addressModel.where({ user_id: userId, is_default: 1, is_delete: 0 }).find();
    }

    if (think.isEmpty(address)) {
      throw this.throwError(-1, addressId ? '收货地址有误' : '请选择收货地址');
    }

    return address;
  }

  /**
   * 是否只有包邮商品
   */
  _isOnlyFreeShipping(goodsList) {
    const free = goodsList.filter(i => i.freight_mode === 1);
    const normal = goodsList.filter(i => i.freight_mode !== 1);
    return free.length > 0 && normal.length === 0;
  }

  /**
   * 检查商品是否可下单并计算价格信息
   */
  async checkGoodsOrder(goodsList, addressId, userId, userAgent, goodsModel, addressModel, selectedFreightTemplateId = null) {
    let freightPrice = 0;
    const { checkPudList, goodsTotalPrice } = await this._checkGoodsAndCalcTotal(goodsList, goodsModel, userAgent);
    const onlyFreeShippingGoods = this._isOnlyFreeShipping(checkPudList);

    // 运费模板分配
    if (selectedFreightTemplateId !== null) {
      if (selectedFreightTemplateId === 0) {
        if (!onlyFreeShippingGoods) throw this.throwError(-1, '当前订单不支持包邮，请选择其他运费模板');
      } else {
        const selectedTemplate = await this.model('freight_template').where({
          id: selectedFreightTemplateId,
          is_delete: 0,
        }).find();

        if (think.isEmpty(selectedTemplate)) {
          throw this.throwError(-1, '指定的运费模板不可用');
        }

        for (const item of checkPudList) {
          item.freight_template_id = selectedFreightTemplateId;
        }
      }
    } else {

      // 如果当前商品只有包邮产品则邮费为0
      if (onlyFreeShippingGoods) {
        freightPrice = 0;
      } else {
        const freightTemplate = await this.model('freight_template').where({
          is_delete: 0,
          freight_use_type: ['IN', [userAgent ? 2 : 1, 3]]
        }).find();

        if (!think.isEmpty(freightTemplate)) {
          for (let item of checkPudList) {
            if (item.freight_mode !== 1 && item.freight_template_id === 0) {
              item.freight_template_id = freightTemplate.id || 0;
            }
          }
        }
      }
    }

    const address = await this._getValidAddress(addressId, userId, addressModel);

    // 运费计算逻辑：只对非包邮商品计算运费
    let addressForCalc = [];
    if (selectedFreightTemplateId === 0) {
      // 选择了包邮模板，只包含包邮商品
      addressForCalc = checkPudList.filter(i => i.freight_mode === 1);
    } else {
      // 其他情况：只计算非包邮商品的运费
      addressForCalc = checkPudList.filter(i => i.freight_mode !== 1);
    }

    const checkedAddress = await addressModel.checkedAddress(address.id, userId, addressForCalc, userAgent);
    if (!think.isEmpty(checkedAddress)) {
      freightPrice = checkedAddress.freight_price;
    }

    return {
      goodsTotalPrice: goodsTotalPrice.toFixed(2),
      freightPrice: freightPrice.toFixed(2),
      checkedGoodsList: checkPudList,
      checkedAddress
    };
  }

  /**
   * 获取所有可用的运费模板并计算运费（按运费从低到高排序）
   */
  async getAvailableFreightTemplates(goodsList, addressId, userId, userAgent, goodsModel, addressModel) {
    const { checkPudList, goodsTotalPrice } = await this._checkGoodsAndCalcTotal(goodsList, goodsModel, userAgent);
    const address = await this._getValidAddress(addressId, userId, addressModel);

    const onlyFreeShippingGoods = this._isOnlyFreeShipping(checkPudList);
    const hasExclusiveTemplate = checkPudList.some(item => item.freight_template_id > 0);

    let allFreightTemplates = [];
    if (hasExclusiveTemplate) {
      const ids = [...new Set(checkPudList.filter(i => i.freight_template_id > 0).map(i => i.freight_template_id))];
      allFreightTemplates = await this.model('freight_template').where({ id: ['IN', ids], is_delete: 0 }).select();
    } else {
      allFreightTemplates = await this.model('freight_template').where({
        is_delete: 0,
        freight_use_type: ['IN', [userAgent ? 2 : 1, 3]]
      }).select();
    }

    if (think.isEmpty(allFreightTemplates)) {
      throw this.throwError(-1, '暂无可用的运费模板');
    }

    const freightTemplateResults = [];

    for (const template of allFreightTemplates) {
      const tempCheckList = checkPudList.map(item => ({
        ...item,
        freight_mode: onlyFreeShippingGoods ? 0 : item.freight_mode,
        freight_template_id: hasExclusiveTemplate
          ? (item.freight_template_id > 0 ? item.freight_template_id : template.id)
          : (item.freight_template_id === 0 ? template.id : item.freight_template_id)
      }));

      // 只对非包邮商品计算运费 - 移除 try-catch，让错误直接抛出
      const nonFreeShippingItems = tempCheckList.filter(item => item.freight_mode !== 1);
      const tempAddress = await addressModel.checkedAddress(address.id, userId, nonFreeShippingItems, userAgent);
      if (!think.isEmpty(tempAddress)) {
        freightTemplateResults.push({
          template_id: template.id,
          template_name: template.name,
          freight_type: template.freight_type,
          freight_use_type: template.freight_use_type,
          freight_price: parseFloat(tempAddress.freight_price),
          description: template.description || ''
        });
      }
    }

    // 如果全部商品包邮，插入包邮模板
    if (onlyFreeShippingGoods) {
      const freeShippingItems = checkPudList.filter(i => i.freight_mode === 1);
      const tempAddress = await addressModel.checkedAddress(address.id, userId, freeShippingItems, userAgent);
      if (!think.isEmpty(tempAddress) && tempAddress.freight_price === 0) {
        freightTemplateResults.unshift({
          template_id: 0,
          template_name: '一件包邮（猫粮/犬粮/猫砂/怡享专用）',
          freight_type: 0,
          freight_use_type: 3,
          freight_price: 0,
          description: ''
        });
      }
    }

    // 附加完整地址信息
    const addressFull = await addressModel.getFullAddress(address.id, userId);
    address.province_name = addressFull.province_name;
    address.city_name = addressFull.city_name;
    address.district_name = addressFull.district_name;
    address.full_region = addressFull.full_region;

    freightTemplateResults.sort((a, b) => a.freight_price - b.freight_price);

    return {
      goodsTotalPrice: goodsTotalPrice.toFixed(2),
      checkedGoodsList: checkPudList,
      checkedAddress: address,
      freightTemplates: freightTemplateResults
    };
  }

  /**
   * 查询当前商品对应的运费模板，并计算运费（按运费从低到高排序）
   */
  async goodsGroupByFreightTemplates(goodsGroupByTemplate, addressId, userId, userAgent, goodsModel, addressModel) {
    // 获取有效地址
    const address = await this._getValidAddress(addressId, userId, addressModel);
    
    // 附加完整地址信息
    const addressFull = await addressModel.getFullAddress(address.id, userId);
    address.province_name = addressFull.province_name;
    address.city_name = addressFull.city_name;
    address.district_name = addressFull.district_name;
    address.full_region = addressFull.full_region;

    const templateGroupResults = [];

    // 处理每个运费模板分组
    for (const [templateKey, goodsList] of Object.entries(goodsGroupByTemplate)) {
        let templateInfo = null;
        let freightPrice = 0;
        let goodsTotalPrice = 0;

        // 为商品添加购买数量和计算总价
        for (const item of goodsList) {
          // 假设从原始请求中能找到购买数量，这里需要你根据实际情况调整
          if (!item.buy_number) {
            item.buy_number = item.number || 1; // 如果没有 buy_number，使用 number 或默认为 1
          }
          
          // 如果是代理商，使用代理价格
          if (userAgent && item.agent_price) {
            item.retail_price = item.agent_price;
          }
          
          const unitPrice = item.retail_price;
          goodsTotalPrice += parseFloat(unitPrice) * parseInt(item.buy_number);
        }

        if (templateKey === 'free') {
          // 包邮商品分组 - 只有一个选项
          templateInfo = [{
            template_id: 0,
            template_name: '一件包邮（猫粮/犬粮/猫砂/怡享专用）',
            freight_type: 0,
            freight_use_type: 3,
            description: '该商品享受包邮服务',
            is_free_shipping: true,
            goods_total_price: goodsTotalPrice.toFixed(2),
            freight_price: '0.00',
            total_price: goodsTotalPrice.toFixed(2),
            is_selected: true // 标记为当前选中的选项
          }];
          freightPrice = 0;
        } else if (templateKey === 'common') {
          // 普通商品分组，查询所有可用的默认运费模板
          const availableTemplates = await this.model('freight_template').where({
            is_delete: 0,
            freight_use_type: ['IN', [userAgent ? 2 : 1, 3]]
          }).order('id ASC').select();

          if (think.isEmpty(availableTemplates)) {
            throw new Error('系统未配置默认运费模版，请联系管理员');
          }

          // 为每个可用模板计算运费
          const templateOptions = [];
          for (const template of availableTemplates) {
            // 为商品设置模板ID
            const tempGoodsList = goodsList.map(item => ({
              ...item,
              freight_template_id: template.id
            }));

            // 计算运费 - 移除 try-catch，让错误直接抛出
            const checkedAddress = await addressModel.checkedAddress(address.id, userId, tempGoodsList, userAgent);
            let templateFreightPrice = 0;
            if (!think.isEmpty(checkedAddress)) {
              templateFreightPrice = checkedAddress.freight_price;
            }

            templateOptions.push({
              template_id: template.id,
              template_name: template.name,
              freight_type: template.freight_type,
              freight_use_type: template.freight_use_type,
              description: template.description || '',
              is_free_shipping: false,
              goods_total_price: goodsTotalPrice.toFixed(2),
              freight_price: templateFreightPrice.toFixed(2),
              total_price: (goodsTotalPrice + templateFreightPrice).toFixed(2)
            });
          }

          // 按运费排序，选择最低运费的模板作为默认选项
          templateOptions.sort((a, b) => parseFloat(a.freight_price) - parseFloat(b.freight_price));

          // 标记第一个（运费最低）为选中状态
          if (templateOptions.length > 0) {
            templateOptions[0].is_selected = true;

            // 为商品设置选中的模板ID
            for (const item of goodsList) {
              item.freight_template_id = templateOptions[0].template_id;
            }

            freightPrice = parseFloat(templateOptions[0].freight_price);
          }

          templateInfo = templateOptions;
        } else {
          // 指定运费模板的商品分组
          const template = await this.model('freight_template').where({
            id: templateKey,
            is_delete: 0
          }).find();

          if (think.isEmpty(template)) {
            // 如果指定的模板不存在，查询所有可用的默认模板
            const availableTemplates = await this.model('freight_template').where({
              is_delete: 0,
              freight_use_type: ['IN', [userAgent ? 2 : 1, 3]]
            }).order('id ASC').select();

            if (think.isEmpty(availableTemplates)) {
              throw new Error('系统未配置默认运费模版，请联系管理员');
            }

            // 为每个可用模板计算运费
            const templateOptions = [];
            for (const fallbackTemplate of availableTemplates) {
              // 为商品设置模板ID
              const tempGoodsList = goodsList.map(item => ({
                ...item,
                freight_template_id: fallbackTemplate.id
              }));

              // 计算运费 - 移除 try-catch，让错误直接抛出
              const checkedAddress = await addressModel.checkedAddress(address.id, userId, tempGoodsList, userAgent);
              let templateFreightPrice = 0;
              if (!think.isEmpty(checkedAddress)) {
                templateFreightPrice = checkedAddress.freight_price;
              }

              templateOptions.push({
                template_id: fallbackTemplate.id,
                template_name: fallbackTemplate.name,
                freight_type: fallbackTemplate.freight_type,
                freight_use_type: fallbackTemplate.freight_use_type,
                description: fallbackTemplate.description || '',
                is_free_shipping: false,
                goods_total_price: goodsTotalPrice.toFixed(2),
                freight_price: templateFreightPrice.toFixed(2),
                total_price: (goodsTotalPrice + templateFreightPrice).toFixed(2),
                is_fallback: true, // 标记为回退到默认模板
                original_template_id: templateKey
              });
            }

            // 按运费排序，选择最低运费的模板作为默认选项
            templateOptions.sort((a, b) => parseFloat(a.freight_price) - parseFloat(b.freight_price));

            // 标记第一个（运费最低）为选中状态
            if (templateOptions.length > 0) {
              templateOptions[0].is_selected = true;

              // 为商品设置选中的模板ID
              for (const item of goodsList) {
                item.freight_template_id = templateOptions[0].template_id;
              }

              freightPrice = parseFloat(templateOptions[0].freight_price);
            }

            templateInfo = templateOptions;
          } else {
            // 指定的模板存在，只返回这一个选项
            // 确保商品的模板ID正确
            for (const item of goodsList) {
              item.freight_template_id = template.id;
            }

            // 计算运费
            const checkedAddress = await addressModel.checkedAddress(address.id, userId, goodsList, userAgent);
            if (!think.isEmpty(checkedAddress)) {
              freightPrice = checkedAddress.freight_price;
            }

            templateInfo = [{
              template_id: template.id,
              template_name: template.name,
              freight_type: template.freight_type,
              freight_use_type: template.freight_use_type,
              description: template.description || '',
              is_free_shipping: false,
              goods_total_price: goodsTotalPrice.toFixed(2),
              freight_price: freightPrice.toFixed(2),
              total_price: (goodsTotalPrice + freightPrice).toFixed(2),
              is_selected: true // 标记为当前选中的选项
            }];
          }
        }

        // 构建分组结果
        templateGroupResults.push({
          template_info: templateInfo,
          goods_list: goodsList.map(item => ({
            goods_id: item.goods_id,
            product_id: item.product_id,
            goods_name: item.goods_name,
            goods_brief: item.goods_brief,
            list_pic_url: item.list_pic_url,
            retail_price: item.retail_price,
            buy_number: item.buy_number,
            subtotal: (parseFloat(item.retail_price) * parseInt(item.buy_number)).toFixed(2), // 添加商品小计
            freight_mode: item.freight_mode,
            freight_template_id: item.freight_template_id,
            goods_weight: item.goods_weight
          }))
        });


    }

    // 按运费价格排序（使用选中模版的运费）
    templateGroupResults.sort((a, b) => {
      const aSelectedTemplate = a.template_info.find(t => t.is_selected) || a.template_info[0];
      const bSelectedTemplate = b.template_info.find(t => t.is_selected) || b.template_info[0];
      return parseFloat(aSelectedTemplate.freight_price) - parseFloat(bSelectedTemplate.freight_price);
    });

    return {
      address: address,
      template_groups: templateGroupResults,
    };
  }
};
