-- 组合支付功能数据库表结构修改脚本
-- 基于现有表结构进行必要的字段添加和修改

-- ========================================
-- 1. 修改 petshop_order 表
-- ========================================

-- 添加组合支付所需的字段
ALTER TABLE `petshop_order` 
ADD COLUMN `primary_pay_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '主支付金额（微信或支付宝）' AFTER `actual_price`,
ADD COLUMN `balance_pay_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '余额支付金额' AFTER `primary_pay_amount`;

-- 修改支付方式字段注释，支持组合支付类型
ALTER TABLE `petshop_order` 
MODIFY COLUMN `pay_type` varchar(20) NOT NULL DEFAULT '' COMMENT '支付方式：ali,wx,offline,balance,ali+bal,wx+bal';

-- 添加索引优化
ALTER TABLE `petshop_order`
ADD INDEX `idx_pay_type` (`pay_type`) USING BTREE;

-- ========================================
-- 2. 修改 petshop_transaction_log 表
-- ========================================

-- 添加余额支付金额字段
ALTER TABLE `petshop_transaction_log`
ADD COLUMN `balance_pay_amount` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '余额支付金额' AFTER `amount`;

-- 修改支付方式字段注释，支持组合支付类型
ALTER TABLE `petshop_transaction_log`
MODIFY COLUMN `pay_type` varchar(255) NOT NULL DEFAULT '' COMMENT '支付方式：ali,wx,offline,balance,ali+bal,wx+bal';

-- 添加索引优化
ALTER TABLE `petshop_transaction_log`
ADD INDEX `idx_user_id` (`user_id`) USING BTREE,
ADD INDEX `idx_log_type` (`log_type`) USING BTREE,
ADD INDEX `idx_pay_status` (`pay_status`) USING BTREE;

-- ========================================
-- 3. 修改 petshop_balance_log 表
-- ========================================

-- 添加关联支付方式字段
ALTER TABLE `petshop_balance_log`
ADD COLUMN `related_pay_type` varchar(20) NOT NULL DEFAULT '' COMMENT '关联的支付方式：ali,wx,offline,balance' AFTER `log_type`;

-- 添加索引优化
ALTER TABLE `petshop_balance_log`
ADD INDEX `idx_log_type` (`log_type`) USING BTREE,
ADD INDEX `idx_order_sn` (`order_sn`) USING BTREE;

-- ========================================
-- 4. 数据迁移和兼容性处理
-- ========================================

-- 为现有订单设置默认的支付金额分配
-- 对于已有的单一支付方式订单，将 actual_price 设置为 primary_pay_amount
UPDATE `petshop_order` 
SET `primary_pay_amount` = `actual_price`, 
    `balance_pay_amount` = 0.00 
WHERE `pay_type` IN ('ali', 'wx', 'offline') 
AND `primary_pay_amount` = 0.00;

-- 对于余额支付订单，将 actual_price 设置为 balance_pay_amount
UPDATE `petshop_order` 
SET `primary_pay_amount` = 0.00, 
    `balance_pay_amount` = `actual_price` 
WHERE `pay_type` = 'balance' 
AND `balance_pay_amount` = 0.00;

-- ========================================
-- 5. 验证脚本（可选执行）
-- ========================================

-- 验证订单表字段是否添加成功
-- SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() 
-- AND TABLE_NAME = 'petshop_order' 
-- AND COLUMN_NAME IN ('primary_pay_amount', 'balance_pay_amount');

-- 验证交易日志表字段是否添加成功
-- SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() 
-- AND TABLE_NAME = 'petshop_transaction_log' 
-- AND COLUMN_NAME = 'balance_pay_amount';

-- 验证余额日志表字段是否添加成功
-- SELECT COLUMN_NAME, COLUMN_TYPE, COLUMN_COMMENT 
-- FROM INFORMATION_SCHEMA.COLUMNS 
-- WHERE TABLE_SCHEMA = DATABASE() 
-- AND TABLE_NAME = 'petshop_balance_log' 
-- AND COLUMN_NAME = 'related_pay_type';

-- ========================================
-- 6. 索引验证（可选执行）
-- ========================================

-- 查看订单表索引
-- SHOW INDEX FROM `petshop_order`;

-- 查看交易日志表索引
-- SHOW INDEX FROM `petshop_transaction_log`;

-- 查看余额日志表索引
-- SHOW INDEX FROM `petshop_balance_log`;