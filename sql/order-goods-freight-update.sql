-- 订单商品运费模版功能数据库表结构修改脚本
-- 将运费模版记录从订单级别改为商品级别

-- ========================================
-- 1. 修改 petshop_order_goods 表
-- ========================================

-- 添加运费模版相关字段
ALTER TABLE `petshop_order_goods` 
ADD COLUMN `freight_template_id` int(11) unsigned NOT NULL DEFAULT '0' COMMENT '运费模版ID' AFTER `goods_specifition_name`,
ADD COLUMN `freight_price` decimal(10,2) unsigned NOT NULL DEFAULT '0.00' COMMENT '该商品的运费' AFTER `freight_template_id`;

-- 添加索引优化
ALTER TABLE `petshop_order_goods`
ADD INDEX `idx_freight_template_id` (`freight_template_id`) USING BTREE;

-- 数据迁移
UPDATE petshop_order_goods og
JOIN petshop_order o ON og.order_id = o.id
SET 
    og.freight_template_id = o.freight_template_id,
    og.freight_price = o.freight_price;