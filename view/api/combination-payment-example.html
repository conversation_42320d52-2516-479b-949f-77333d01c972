<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>组合支付示例</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 400px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .payment-container {
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .order-info {
            border-bottom: 1px solid #eee;
            padding-bottom: 15px;
            margin-bottom: 20px;
        }
        
        .order-amount {
            font-size: 24px;
            font-weight: bold;
            color: #ff6b35;
            text-align: center;
        }
        
        .balance-info {
            background: #f8f9fa;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .balance-row {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin: 8px 0;
        }
        
        .balance-amount {
            font-weight: bold;
            color: #28a745;
        }
        
        .amount-input {
            display: flex;
            align-items: center;
            margin: 10px 0;
        }
        
        .amount-input label {
            width: 80px;
            font-weight: 500;
        }
        
        .amount-input input {
            flex: 1;
            padding: 8px 12px;
            border: 1px solid #ddd;
            border-radius: 6px;
            font-size: 16px;
        }
        
        .amount-input .currency {
            margin-left: 8px;
            color: #666;
        }
        
        .slider-container {
            margin: 15px 0;
        }
        
        .slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }
        
        .slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: #ff6b35;
            cursor: pointer;
        }
        
        .payment-breakdown {
            background: #e3f2fd;
            border-radius: 8px;
            padding: 15px;
            margin: 15px 0;
        }
        
        .breakdown-row {
            display: flex;
            justify-content: space-between;
            margin: 5px 0;
        }
        
        .breakdown-total {
            border-top: 1px solid #ccc;
            padding-top: 8px;
            margin-top: 8px;
            font-weight: bold;
        }
        
        .payment-buttons {
            margin-top: 20px;
        }
        
        .pay-button {
            width: 100%;
            padding: 15px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: bold;
            cursor: pointer;
            margin: 8px 0;
            transition: all 0.3s;
        }
        
        .pay-button.wechat {
            background: #07c160;
            color: white;
        }
        
        .pay-button.alipay {
            background: #1677ff;
            color: white;
        }
        
        .pay-button.balance {
            background: #ff9800;
            color: white;
        }
        
        .pay-button:hover {
            opacity: 0.9;
            transform: translateY(-1px);
        }
        
        .pay-button:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
        }
        
        .error-message {
            color: #dc3545;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .success-message {
            color: #28a745;
            font-size: 14px;
            margin-top: 5px;
        }
        
        .payment-mode {
            margin: 20px 0;
        }
        
        .mode-tabs {
            display: flex;
            border-bottom: 1px solid #eee;
        }
        
        .mode-tab {
            flex: 1;
            padding: 10px;
            text-align: center;
            cursor: pointer;
            border-bottom: 2px solid transparent;
            transition: all 0.3s;
        }
        
        .mode-tab.active {
            border-bottom-color: #ff6b35;
            color: #ff6b35;
            font-weight: bold;
        }
        
        .mode-content {
            padding: 15px 0;
        }
        
        .hidden {
            display: none;
        }
    </style>
</head>
<body>
    <div class="payment-container">
        <div class="order-info">
            <h3>订单支付</h3>
            <div class="order-amount">¥<span id="orderAmount">128.50</span></div>
            <p>订单号：<span id="orderSn">202501051234567</span></p>
        </div>
        
        <div class="balance-info">
            <div class="balance-row">
                <span>当前余额</span>
                <span class="balance-amount">¥<span id="currentBalance">85.30</span></span>
            </div>
        </div>
        
        <div class="payment-mode">
            <div class="mode-tabs">
                <div class="mode-tab active" data-mode="single">单一支付</div>
                <div class="mode-tab" data-mode="combination">组合支付</div>
            </div>
            
            <!-- 单一支付模式 -->
            <div class="mode-content" id="singlePayMode">
                <div class="payment-buttons">
                    <button class="pay-button wechat" onclick="singlePay('wx')">
                        微信支付 ¥<span class="order-amount-text">128.50</span>
                    </button>
                    <button class="pay-button alipay" onclick="singlePay('ali')">
                        支付宝支付 ¥<span class="order-amount-text">128.50</span>
                    </button>
                    <button class="pay-button balance" onclick="singlePay('balance')" disabled>
                        余额支付 ¥<span class="order-amount-text">128.50</span>
                        <div class="error-message">余额不足</div>
                    </button>
                </div>
            </div>
            
            <!-- 组合支付模式 -->
            <div class="mode-content hidden" id="combinationPayMode">
                <div class="slider-container">
                    <label>使用余额：</label>
                    <input type="range" class="slider" id="balanceSlider" 
                           min="0" max="85.30" step="0.01" value="85.30"
                           oninput="updateAmountAllocation()">
                </div>
                
                <div class="amount-input">
                    <label>余额：</label>
                    <input type="number" id="balanceAmount" value="85.30" 
                           min="0" max="85.30" step="0.01"
                           oninput="updateFromInput('balance')">
                    <span class="currency">元</span>
                </div>
                
                <div class="amount-input">
                    <label>第三方：</label>
                    <input type="number" id="thirdPartyAmount" value="43.20" 
                           min="0" step="0.01" readonly>
                    <span class="currency">元</span>
                </div>
                
                <div class="payment-breakdown">
                    <div class="breakdown-row">
                        <span>余额支付</span>
                        <span>¥<span id="breakdownBalance">85.30</span></span>
                    </div>
                    <div class="breakdown-row">
                        <span>第三方支付</span>
                        <span>¥<span id="breakdownThirdParty">43.20</span></span>
                    </div>
                    <div class="breakdown-row breakdown-total">
                        <span>合计</span>
                        <span>¥<span id="breakdownTotal">128.50</span></span>
                    </div>
                </div>
                
                <div class="payment-buttons">
                    <button class="pay-button wechat" onclick="combinationPay('wx')">
                        微信+余额支付
                    </button>
                    <button class="pay-button alipay" onclick="combinationPay('ali')">
                        支付宝+余额支付
                    </button>
                </div>
                
                <div id="validationMessage"></div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let orderData = {
            orderId: 12345,
            orderSn: '202501051234567',
            orderAmount: 128.50,
            currentBalance: 85.30
        };

        // 初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateDisplay();
            checkBalanceSufficiency();
        });

        // 更新显示
        function updateDisplay() {
            document.getElementById('orderAmount').textContent = orderData.orderAmount.toFixed(2);
            document.getElementById('orderSn').textContent = orderData.orderSn;
            document.getElementById('currentBalance').textContent = orderData.currentBalance.toFixed(2);
            
            // 更新所有订单金额显示
            document.querySelectorAll('.order-amount-text').forEach(el => {
                el.textContent = orderData.orderAmount.toFixed(2);
            });
            
            // 更新滑块最大值
            const slider = document.getElementById('balanceSlider');
            const maxBalance = Math.min(orderData.currentBalance, orderData.orderAmount);
            slider.max = maxBalance;
            slider.value = maxBalance;
            
            // 更新余额输入框
            document.getElementById('balanceAmount').max = maxBalance;
            document.getElementById('balanceAmount').value = maxBalance.toFixed(2);
            
            updateAmountAllocation();
        }

        // 检查余额是否足够单独支付
        function checkBalanceSufficiency() {
            const balanceButton = document.querySelector('.pay-button.balance');
            if (orderData.currentBalance >= orderData.orderAmount) {
                balanceButton.disabled = false;
                balanceButton.querySelector('.error-message').style.display = 'none';
            } else {
                balanceButton.disabled = true;
                balanceButton.querySelector('.error-message').style.display = 'block';
            }
        }

        // 切换支付模式
        document.querySelectorAll('.mode-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const mode = this.dataset.mode;
                
                // 更新标签状态
                document.querySelectorAll('.mode-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 切换内容显示
                document.getElementById('singlePayMode').classList.toggle('hidden', mode !== 'single');
                document.getElementById('combinationPayMode').classList.toggle('hidden', mode !== 'combination');
            });
        });

        // 更新金额分配
        function updateAmountAllocation() {
            const balanceAmount = parseFloat(document.getElementById('balanceSlider').value);
            const thirdPartyAmount = orderData.orderAmount - balanceAmount;
            
            // 更新输入框
            document.getElementById('balanceAmount').value = balanceAmount.toFixed(2);
            document.getElementById('thirdPartyAmount').value = thirdPartyAmount.toFixed(2);
            
            // 更新分解显示
            document.getElementById('breakdownBalance').textContent = balanceAmount.toFixed(2);
            document.getElementById('breakdownThirdParty').textContent = thirdPartyAmount.toFixed(2);
            document.getElementById('breakdownTotal').textContent = orderData.orderAmount.toFixed(2);
            
            // 验证金额分配
            validateAmountAllocation();
        }

        // 从输入框更新
        function updateFromInput(type) {
            if (type === 'balance') {
                const balanceAmount = parseFloat(document.getElementById('balanceAmount').value) || 0;
                const maxBalance = Math.min(orderData.currentBalance, orderData.orderAmount);
                
                if (balanceAmount > maxBalance) {
                    document.getElementById('balanceAmount').value = maxBalance.toFixed(2);
                    return;
                }
                
                document.getElementById('balanceSlider').value = balanceAmount;
                updateAmountAllocation();
            }
        }

        // 验证金额分配
        function validateAmountAllocation() {
            const balanceAmount = parseFloat(document.getElementById('balanceAmount').value) || 0;
            const thirdPartyAmount = parseFloat(document.getElementById('thirdPartyAmount').value) || 0;
            const total = balanceAmount + thirdPartyAmount;
            
            const messageEl = document.getElementById('validationMessage');
            const buttons = document.querySelectorAll('#combinationPayMode .pay-button');
            
            let isValid = true;
            let message = '';
            
            if (Math.abs(total - orderData.orderAmount) > 0.01) {
                isValid = false;
                message = '金额分配错误，总额不匹配';
            } else if (balanceAmount > orderData.currentBalance) {
                isValid = false;
                message = '余额不足';
            } else if (balanceAmount <= 0) {
                isValid = false;
                message = '余额使用金额必须大于0';
            } else if (thirdPartyAmount <= 0) {
                isValid = false;
                message = '第三方支付金额必须大于0';
            }
            
            if (isValid) {
                messageEl.innerHTML = '<div class="success-message">金额分配有效</div>';
                buttons.forEach(btn => btn.disabled = false);
            } else {
                messageEl.innerHTML = `<div class="error-message">${message}</div>`;
                buttons.forEach(btn => btn.disabled = true);
            }
        }

        // 单一支付
        function singlePay(payType) {
            const payTypeNames = {
                'wx': '微信支付',
                'ali': '支付宝支付',
                'balance': '余额支付'
            };
            
            if (payType === 'balance' && orderData.currentBalance < orderData.orderAmount) {
                alert('余额不足，无法完成支付');
                return;
            }
            
            // 模拟支付请求
            console.log(`发起${payTypeNames[payType]}`, {
                orderId: orderData.orderId,
                orderSn: orderData.orderSn,
                payType: payType,
                amount: orderData.orderAmount
            });
            
            alert(`正在跳转到${payTypeNames[payType]}页面...`);
        }

        // 组合支付
        function combinationPay(payType) {
            const balanceAmount = parseFloat(document.getElementById('balanceAmount').value);
            const thirdPartyAmount = parseFloat(document.getElementById('thirdPartyAmount').value);
            
            const payTypeNames = {
                'wx': '微信',
                'ali': '支付宝'
            };
            
            // 模拟支付请求
            console.log(`发起${payTypeNames[payType]}+余额组合支付`, {
                orderId: orderData.orderId,
                orderSn: orderData.orderSn,
                balanceAmount: balanceAmount,
                thirdPartyAmount: thirdPartyAmount,
                payType: payType
            });
            
            alert(`正在处理组合支付：余额${balanceAmount.toFixed(2)}元 + ${payTypeNames[payType]}${thirdPartyAmount.toFixed(2)}元`);
        }

        // 模拟获取用户余额
        async function getUserBalance() {
            // 这里应该调用实际的API
            try {
                const response = await fetch('/api/pay/getUserBalance', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    }
                });
                const result = await response.json();
                if (result.errno === 0) {
                    orderData.currentBalance = result.data.balance;
                    updateDisplay();
                    checkBalanceSufficiency();
                }
            } catch (error) {
                console.error('获取余额失败:', error);
            }
        }

        // 模拟验证金额分配
        async function validateCombinationAmount() {
            const balanceAmount = parseFloat(document.getElementById('balanceAmount').value);
            const thirdPartyAmount = parseFloat(document.getElementById('thirdPartyAmount').value);
            
            try {
                const response = await fetch('/api/pay/validateCombinationAmount', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({
                        orderId: orderData.orderId,
                        balanceAmount: balanceAmount,
                        thirdPartyAmount: thirdPartyAmount
                    })
                });
                const result = await response.json();
                console.log('验证结果:', result);
            } catch (error) {
                console.error('验证失败:', error);
            }
        }
    </script>
</body>
</html>